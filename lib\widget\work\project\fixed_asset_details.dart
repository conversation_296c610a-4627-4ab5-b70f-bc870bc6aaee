import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/classes/fixedProperty.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';


class ProjectFixedAssetDetails extends StatefulWidget {
  final NewarcProject? project;
  final List<bool>? isInputChangeDetected;

  const ProjectFixedAssetDetails(
      {Key? key, this.project, this.isInputChangeDetected})
      : super(key: key);

  @override
  State<ProjectFixedAssetDetails> createState() => _ProjectFixedAssetDetails();
}

class _ProjectFixedAssetDetails extends State<ProjectFixedAssetDetails> {
  bool loading = false;
  TextEditingController? txtconPropertyName = new TextEditingController();
  TextEditingController? txtconCity = new TextEditingController();

  TextEditingController? txtconPropertyZoneName = new TextEditingController();
  TextEditingController? txtconPropertyTypeName = new TextEditingController();
  TextEditingController? txtconInstallments = new TextEditingController();
  TextEditingController? txtconAreaMq = new TextEditingController();
  TextEditingController? txtconLocals = new TextEditingController();
  TextEditingController? txtconFloors = new TextEditingController();
  TextEditingController? txtconBaths = new TextEditingController();
  TextEditingController? txtconRefurbishmentValue = new TextEditingController();

  TextEditingController? txtconReferenceFirstName = new TextEditingController();
  TextEditingController? txtconReferenceLastName = new TextEditingController();
  TextEditingController? txtconReferencePhone = new TextEditingController();
  TextEditingController? txtconReferenceEmail = new TextEditingController();

  TextEditingController? txtconPropertyPurchase = new TextEditingController();
  TextEditingController? txtconDeposit = new TextEditingController();
  TextEditingController? txtconNotaryCost = new TextEditingController();
  TextEditingController? txtconRegistrationTax = new TextEditingController();
  TextEditingController? txtconApe = new TextEditingController();
  TextEditingController? txtconCondominiumFees = new TextEditingController();
  TextEditingController? txtconLoadUnloadInstallation =
      new TextEditingController();
  TextEditingController? txtconRegistrationTax2 = new TextEditingController();
  TextEditingController? txtconElectricalCond = new TextEditingController();
  TextEditingController? txtconLight = new TextEditingController();
  TextEditingController? txtconMoveGasCont = new TextEditingController();
  TextEditingController? txtconHeating = new TextEditingController();

  TextEditingController? txtconPosthumousInsurance = new TextEditingController();
  TextEditingController? txtconCarInsurance = new TextEditingController();
  TextEditingController? txtconWeldingCostCont = new TextEditingController();
  
  bool propertyPurchase = false;
  bool deposit = false;
  bool notaryCost = false;
  bool registrationTax = false;

  bool ape = false;
  bool condominiumFees = false;
  bool loadUnloadInstallation = false;
  bool registrationTax2 = false;
  bool electricalCond = false;
  bool light = false;
  bool moveGasCont = false;
  bool heating = false;

  bool posthumousInsurance = false;
  bool carInsurance = false;
  bool weldingCost = false;

  List<String> typesList = appConst.propertyTypesList;

  List<String> installmentList = appConst.installmentList;
  Map<String, List<String>> cityZones = appConst.cityZones;
  
  List<String> cities = appConst.cities;

  List<String> localsList = List<String>.generate(10, (i) => (i).toString());
  List<String> floorsList = List<String>.generate(15, (i) => (i).toString());
  List<String> bathsList = List<String>.generate(10, (i) => (i).toString());

  String progressMessage = '';


  @override
  void initState() {
    // TODO: implement initState
    cityZones.forEach((key, value) {
      value.sort();
      value.map((zone)=>zone.toTitleCase());
      });
    super.initState();
    setInitialValues();


    txtconPropertyName!.addListener(_checkForChanges);
    txtconPropertyZoneName!.addListener(_checkForChanges);
    txtconPropertyTypeName!.addListener(_checkForChanges);
    txtconAreaMq!.addListener(_checkForChanges);
    txtconLocals!.addListener(_checkForChanges);
    txtconFloors!.addListener(_checkForChanges);
    txtconBaths!.addListener(_checkForChanges);
    txtconReferenceFirstName!.addListener(_checkForChanges);
    txtconReferenceLastName!.addListener(_checkForChanges);
    txtconReferencePhone!.addListener(_checkForChanges);
    txtconReferenceEmail!.addListener(_checkForChanges);
    txtconPropertyPurchase!.addListener(_checkForChanges);
    txtconDeposit!.addListener(_checkForChanges);
    txtconNotaryCost!.addListener(_checkForChanges);
    txtconRegistrationTax!.addListener(_checkForChanges);
    txtconApe!.addListener(_checkForChanges);
    txtconCondominiumFees!.addListener(_checkForChanges);
    txtconLoadUnloadInstallation!.addListener(_checkForChanges);
    txtconRegistrationTax2!.addListener(_checkForChanges);
    txtconElectricalCond!.addListener(_checkForChanges);
    txtconLight!.addListener(_checkForChanges);
    txtconMoveGasCont!.addListener(_checkForChanges);
    txtconHeating!.addListener(_checkForChanges);
    txtconPosthumousInsurance!.addListener(_checkForChanges);
    txtconCarInsurance!.addListener(_checkForChanges);
    txtconWeldingCostCont!.addListener(_checkForChanges);
  }

  @override
  void dispose() {
    txtconPropertyName!.removeListener(_checkForChanges);
    txtconPropertyZoneName!.removeListener(_checkForChanges);
    txtconPropertyTypeName!.removeListener(_checkForChanges);
    txtconAreaMq!.removeListener(_checkForChanges);
    txtconLocals!.removeListener(_checkForChanges);
    txtconFloors!.removeListener(_checkForChanges);
    txtconBaths!.removeListener(_checkForChanges);
    txtconReferenceFirstName!.removeListener(_checkForChanges);
    txtconReferenceLastName!.removeListener(_checkForChanges);
    txtconReferencePhone!.removeListener(_checkForChanges);
    txtconReferenceEmail!.removeListener(_checkForChanges);
    txtconPropertyPurchase!.removeListener(_checkForChanges);
    txtconDeposit!.removeListener(_checkForChanges);
    txtconNotaryCost!.removeListener(_checkForChanges);
    txtconRegistrationTax!.removeListener(_checkForChanges);
    txtconApe!.removeListener(_checkForChanges);
    txtconCondominiumFees!.removeListener(_checkForChanges);
    txtconLoadUnloadInstallation!.removeListener(_checkForChanges);
    txtconRegistrationTax2!.removeListener(_checkForChanges);
    txtconElectricalCond!.removeListener(_checkForChanges);
    txtconLight!.removeListener(_checkForChanges);
    txtconMoveGasCont!.removeListener(_checkForChanges);
    txtconHeating!.removeListener(_checkForChanges);
    txtconPosthumousInsurance!.removeListener(_checkForChanges);
    txtconCarInsurance!.removeListener(_checkForChanges);
    txtconWeldingCostCont!.removeListener(_checkForChanges);

    super.dispose();
  }

  void _checkForChanges() {
    setState(() {
      widget.isInputChangeDetected![0] = true;
    });
  }

  @protected
  void didUpdateWidget(ProjectFixedAssetDetails oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitialValues();
  }

  setInitialValues() async{
    setState(() {
      loading = true;
    });
    FixedProperty fixedProperty = widget.project!.fixedProperty!;

    try {
      txtconPropertyName!.text = fixedProperty.propertyName!;
      txtconCity!.text = fixedProperty.city == null ? '' : fixedProperty.city!;

      txtconPropertyZoneName!.text = fixedProperty.zone != null ? fixedProperty.zone! : '';
      txtconPropertyTypeName!.text = fixedProperty.propertyType != null ? fixedProperty.propertyType! : '';
      txtconAreaMq!.text = fixedProperty.areaMQ != null ? fixedProperty.areaMQ!.toString() : '';
      txtconLocals!.text = fixedProperty.locals != null ? fixedProperty.locals!.toString() : '';
      txtconFloors!.text = fixedProperty.floors != null ? fixedProperty.floors!.toString() : '';
      txtconBaths!.text = fixedProperty.baths != null ? fixedProperty.baths.toString() : '';

      txtconReferenceFirstName!.text = fixedProperty.refFirstName ?? "";
      txtconReferenceLastName!.text = fixedProperty.refLastName ?? "";
      txtconReferencePhone!.text = fixedProperty.refPhone ?? "";
      txtconReferenceEmail!.text = fixedProperty.refEmail ?? "";

      txtconPropertyPurchase!.text = fixedProperty.purchaseCost != null
          ? fixedProperty.purchaseCost!.toString()
          : '';
      txtconDeposit!.text = fixedProperty.deposit != null
          ? fixedProperty.deposit!.toString()
          : '';
      txtconNotaryCost!.text = fixedProperty.notaryCost != null
          ? fixedProperty.notaryCost!.toString()
          : '';
      txtconRegistrationTax!.text = fixedProperty.registrationTax != null
          ? fixedProperty.registrationTax!.toString()
          : '';
      txtconApe!.text =
          fixedProperty.ape != null ? fixedProperty.ape!.toString() : '';
      txtconCondominiumFees!.text = fixedProperty.condominiumFees != null
          ? fixedProperty.condominiumFees!.toString()
          : '';
      txtconLoadUnloadInstallation!.text =
          fixedProperty.loadUnloadInstallation != null
              ? fixedProperty.loadUnloadInstallation!.toString()
              : '';
      // txtconRegistrationTax2!.text = fixedProperty.registrationTax2 != null ? fixedProperty.registrationTax2!.toString() : '';
      txtconElectricalCond!.text = fixedProperty.electricalCond != null
          ? fixedProperty.electricalCond!.toString()
          : '';
      txtconLight!.text =
          fixedProperty.light != null ? fixedProperty.light.toString() : '';
      txtconMoveGasCont!.text = fixedProperty.moveGasCont != null
          ? fixedProperty.moveGasCont.toString()
          : '';
      txtconHeating!.text = fixedProperty.heating != null ? fixedProperty.heating.toString() : '';
      
      txtconCarInsurance!.text = fixedProperty.carInsurance != null ? fixedProperty.carInsurance.toString() : '';
      txtconWeldingCostCont!.text = fixedProperty.weldingCost != null ? fixedProperty.weldingCost.toString() : '';
      txtconPosthumousInsurance!.text = fixedProperty.posthumousInsurance != null ? fixedProperty.posthumousInsurance.toString() : '';

      propertyPurchase = fixedProperty.purchaseCost != null ? true : false;
      deposit = fixedProperty.deposit != null ? true : false;
      notaryCost = fixedProperty.notaryCost != null ? true : false;
      registrationTax = fixedProperty.registrationTax != null ? true : false;
      ape = fixedProperty.ape != null ? true : false;
      condominiumFees = fixedProperty.condominiumFees != null ? true : false;
      loadUnloadInstallation = fixedProperty.loadUnloadInstallation != null ? true : false;
      // registrationTax2 = fixedProperty.isRegistrationTax2!;
      electricalCond = fixedProperty.electricalCond != null ? true : false;
      light = fixedProperty.light != null ? true : false;
      moveGasCont = fixedProperty.moveGasCont != null ? true : false;
      heating = fixedProperty.heating != null ? true : false;

      posthumousInsurance = fixedProperty.posthumousInsurance != null ? true : false;
      carInsurance = fixedProperty.carInsurance != null ? true : false;
      weldingCost = fixedProperty.weldingCost != null ? true : false;


      if( widget.project!.type == 'Ristrutturazione' ) {
        if( widget.project!.fixedProperty!.refurbishmentInstallments != null ) {
          txtconInstallments!.text = widget.project!.fixedProperty!.refurbishmentInstallments!;
        }
        if( widget.project!.fixedProperty!.refurbishmentValue != null ) {
          txtconRefurbishmentValue!.text = widget.project!.fixedProperty!.refurbishmentValue!.toString();
        }
        
      }
      setState(() {
        loading = false;
      });
    } catch (e, s) {
      setState(() {
        loading = false;
      });
      print({e, s});
    }
  }

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? Center(child: NarFormLabelWidget(label: 'Loading'))
          : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        SizedBox(height: 20),
                        NarFormLabelWidget(
                          label: 'Immobile',
                          fontSize: 20,
                          fontWeight: 'bold',
                        ),
                        SizedBox(height: 30),
                        Container(
                            // color: Colors.grey,
                            // width: 200,
                            padding: EdgeInsets.symmetric(
                                vertical: 20, horizontal: 15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: Color.fromRGBO(230, 230, 230, 1),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Caratteristiche immobile',
                                  fontSize: 17,
                                  fontWeight: '700',
                                  textColor: Color.fromRGBO(0, 0, 0, 1),
                                ),
                                SizedBox(height: 10),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextFormField(
                                        flex: 1,
                                        label: 'Indirizzo completo',
                                        hintText: "",
                                        controller: txtconPropertyName,
                                        enabled: false,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Required';
                                          }
                                          return null;
                                        }),
                                    SizedBox(width: 17),
                                    CustomTextFormField(
                                        flex: 1,
                                        label: 'Città',
                                        hintText: "",
                                        controller: txtconCity,
                                        enabled: false,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Required';
                                          }
                                          return null;
                                        }),
                                    SizedBox(width: 17),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          (txtconCity!.text == '' || txtconCity?.text == null || !cityZones.keys.contains(txtconCity!.text))
                                              ? Container()
                                              :
                                          NarSelectBoxWidget(
                                            label: "Zona",
                                            options: (txtconCity!.text == '' || txtconCity?.text == null || !cityZones.keys.contains(txtconCity!.text)) 
                                                      ? [] 
                                                      : cityZones[txtconCity!.text]!.map((element) {
                                                          return element.toTitleCase();
                                                        }).toList(),
                                            controller: txtconPropertyZoneName,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Expanded(
                                      flex: 4,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarSelectBoxWidget(
                                            label: "Tipologia",
                                            flex: 3,
                                            options: typesList,
                                            controller: txtconPropertyTypeName,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 17),
                                    CustomTextFormField(
                                      flex: 2,
                                      label: 'Mq',
                                      hintText: "",
                                      controller: txtconAreaMq,
                                    ),
                                    SizedBox(width: 17),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarSelectBoxWidget(
                                            label: "Locali",
                                            options: localsList,
                                            controller: txtconLocals,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 17),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarSelectBoxWidget(
                                            label: "Piano",
                                            options: appConst.getFloorsList(),
                                            controller: txtconFloors,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 17),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarSelectBoxWidget(
                                            label: 'Bagni',
                                            options: bathsList,
                                            controller: txtconBaths,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            )),
                        SizedBox(height: 15),

                        // Proprietario/Cliente
                        widget.project!.type!.toLowerCase() ==
                                "ristrutturazione"
                            ? Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 20, horizontal: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: Color.fromRGBO(230, 230, 230, 1),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Proprietario/Cliente',
                                      fontSize: 17,
                                      fontWeight: '700',
                                      textColor: Color.fromRGBO(0, 0, 0, 1),
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomTextFormField(
                                            flex: 3,
                                            label: 'Nome riferimento',
                                            hintText: "",
                                            enabled: false,
                                            controller: txtconReferenceFirstName,
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Required';
                                              }
                                              return null;
                                            }),
                                        SizedBox(width: 17),
                                        CustomTextFormField(
                                            flex: 3,
                                            label: 'Cognome riferimento',
                                            hintText: "",
                                            enabled: false,
                                            controller: txtconReferenceLastName,
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Required';
                                              }
                                              return null;
                                            }),
                                      ],
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomTextFormField(
                                            flex: 3,
                                            label: 'Telefono',
                                            hintText: "",
                                            enabled: false,
                                            controller: txtconReferencePhone,
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Required';
                                              }
                                              return null;
                                            }),
                                        SizedBox(width: 17),
                                        CustomTextFormField(
                                            flex: 3,
                                            label: 'Email',
                                            hintText: "",
                                            enabled: false,
                                            controller: txtconReferenceEmail,
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Required';
                                              }
                                              if (!value
                                                  .toString()
                                                  .isValidEmail()) {
                                                return 'Invalid email';
                                              }
                                              return null;
                                            }),
                                      ],
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox(height: 0),

                        widget.project!.type!.toLowerCase() ==
                                "ristrutturazione"
                            ? SizedBox(height: 15)
                            : SizedBox(height: 0),
                        // Costi immobile
                        widget.project!.type!.toLowerCase() ==
                                "ristrutturazione"
                            ? Container()
                            : Container(
                                // color: Colors.grey,
                                // width: 200,
                                padding: EdgeInsets.symmetric(
                                    vertical: 20, horizontal: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: Color.fromRGBO(230, 230, 230, 1),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Costi immobile',
                                      fontSize: 17,
                                      fontWeight: '700',
                                      textColor: Color.fromRGBO(0, 0, 0, 1),
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label:
                                                          'Acquisto/Ricavo gar',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: propertyPurchase,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          setState(() {
                                                            _checkForChanges();

                                                            txtconPropertyPurchase!
                                                                .text = '';
                                                            propertyPurchase =
                                                                value;
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller:
                                                      txtconPropertyPurchase,
                                                  enabled: propertyPurchase),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(child: SizedBox(height: 0))
                                        // Expanded(
                                        //   flex: 1,
                                        //   child: Row(
                                        //     mainAxisSize: MainAxisSize.min,
                                        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        //     children: [
                                        //       Expanded(
                                        //         flex: 1,
                                        //         child: Column(
                                        //           mainAxisSize: MainAxisSize.max,
                                        //           mainAxisAlignment: MainAxisAlignment.start,
                                        //           crossAxisAlignment: CrossAxisAlignment.start,
                                        //           children: [
                                        //             NarFormLabelWidget(
                                        //               label: 'Ricavo garantito',
                                        //               fontSize: 12,
                                        //               textColor:  Color(0xff696969),
                                        //             ),
                                        //             SizedBox(height:4),
                                        //             Padding(
                                        //               padding: const EdgeInsets.only( top: 6.0, bottom: 18, left: 0),
                                        //               child: Switch(
                                        //                 value: guaranteedRevenut,
                                        //                 activeColor: Theme.of(context).primaryColor,
                                        //                 onChanged: (bool value) async {
                                        //                   setState(() {
                                        //                     guaranteedRevenut = value;
                                        //                     txtconGuaranteedRevenue!.text = '';
                                        //                   });
                                        //                 },
                                        //               ),
                                        //             ),
                                        //           ],
                                        //         ),
                                        //       ),
                                        //       CustomTextFormField(
                                        //         flex: 1,
                                        //         label: 'Costo',
                                        //         hintText: "",
                                        //         controller: txtconGuaranteedRevenue,
                                        //         enabled: guaranteedRevenut
                                        //       ),
                                        //     ],
                                        //   ),
                                        // ),
                                      ],
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          SizedBox(
                                            width: 50,
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Expanded(
                                                  flex: 1,
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                top: 0,
                                                                bottom: 0,
                                                                left: 0),
                                                        child: Switch(
                                                          value: deposit,
                                                          activeColor:
                                                              Theme.of(context)
                                                                  .primaryColor,
                                                          onChanged: (bool
                                                              value) async {
                                                            _checkForChanges();
                                                            setState(() {
                                                              deposit = value;
                                                              txtconDeposit!
                                                                  .text = '';
                                                            });
                                                          },
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 5,
                                                      ),
                                                      NarFormLabelWidget(
                                                        label: 'Caparra',
                                                        fontSize: 15,
                                                        textColor:
                                                            Color(0xff696969),
                                                        fontWeight: 'bold',
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                CustomTextFormField(
                                                    flex: 1,
                                                    label: 'Caparra',
                                                    hintText: "",
                                                    controller: txtconDeposit,
                                                    enabled: deposit),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: SizedBox(height: 0),
                                          )
                                        ]),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Costi notarili',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: notaryCost,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            notaryCost = value;
                                                            txtconNotaryCost!
                                                                .text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconNotaryCost,
                                                  enabled: notaryCost),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label:
                                                          'Imposta di registro',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: registrationTax,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            registrationTax =
                                                                value;
                                                            txtconRegistrationTax!
                                                                .text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller:
                                                      txtconRegistrationTax,
                                                  enabled: registrationTax),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10),
                                  ],
                                )),

                        SizedBox(height: 15),

                        // Costi vari
                        widget.project!.type!.toLowerCase() ==
                                "ristrutturazione"
                            ? Container()
                            : Container(
                                // color: Colors.grey,
                                // width: 200,
                                padding: EdgeInsets.symmetric(
                                    vertical: 20, horizontal: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: Color.fromRGBO(230, 230, 230, 1),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Costi vari',
                                      fontSize: 17,
                                      fontWeight: '700',
                                      textColor: Color.fromRGBO(0, 0, 0, 1),
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'APE',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: ape,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            txtconApe!.text =
                                                                '';
                                                            ape = value;
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconApe,
                                                  enabled: ape),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label:
                                                          'Spese condominiali',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: condominiumFees,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            condominiumFees =
                                                                value;
                                                            txtconCondominiumFees!
                                                                .text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller:
                                                      txtconCondominiumFees,
                                                  enabled: condominiumFees),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label:
                                                          'Carico/Scar. Impianti',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value:
                                                            loadUnloadInstallation,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            loadUnloadInstallation =
                                                                value;
                                                            txtconLoadUnloadInstallation!
                                                                .text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller:
                                                      txtconLoadUnloadInstallation,
                                                  enabled:
                                                      loadUnloadInstallation),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label:
                                                          'Elettricista cond.',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: electricalCond,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            electricalCond =
                                                                value;
                                                            txtconElectricalCond!
                                                                .text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller:
                                                      txtconElectricalCond,
                                                  enabled: electricalCond),
                                            ],
                                          ),
                                        ),

                                        // Expanded(
                                        //   flex: 1,
                                        //   child: Row(
                                        //     mainAxisSize: MainAxisSize.min,
                                        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        //     children: [
                                        //       Expanded(
                                        //         flex: 1,
                                        //         child: Column(
                                        //           mainAxisSize: MainAxisSize.max,
                                        //           mainAxisAlignment: MainAxisAlignment.start,
                                        //           crossAxisAlignment: CrossAxisAlignment.start,
                                        //           children: [
                                        //             NarFormLabelWidget(
                                        //               label: 'Imposta di registro',
                                        //               fontSize: 12,
                                        //               textColor:  Color(0xff696969),
                                        //             ),
                                        //             SizedBox(height:4),
                                        //             Padding(
                                        //               padding: const EdgeInsets.only( top: 6.0, bottom: 18, left: 0),
                                        //               child: Switch(
                                        //                 value: registrationTax2,
                                        //                 activeColor: Theme.of(context).primaryColor,
                                        //                 onChanged: (bool value) async {
                                        //                   setState(() {
                                        //                     registrationTax2 = value;
                                        //                   });
                                        //                 },
                                        //               ),
                                        //             ),
                                        //           ],
                                        //         ),
                                        //       ),
                                        //       CustomTextFormField(
                                        //         flex: 1,
                                        //         label: 'Costo',
                                        //         hintText: "",
                                        //         controller: txtconRegistrationTax2,
                                        //         enabled: registrationTax2
                                        //       ),
                                        //     ],
                                        //   ),
                                        // ),
                                      ],
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Luce',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: light,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            light = value;
                                                            txtconLight!.text =
                                                                '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconLight,
                                                  enabled: light),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Spost. Cont. Gas',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: moveGasCont,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            txtconMoveGasCont!
                                                                .text = '';
                                                            moveGasCont = value;
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconMoveGasCont,
                                                  enabled: moveGasCont),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Riscaldamento',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: heating,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            heating = value;
                                                            txtconHeating!
                                                                .text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconHeating,
                                                  enabled: heating),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Opere di saldatura',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: weldingCost,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            txtconWeldingCostCont!
                                                                .text = '';
                                                            weldingCost = value;
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconWeldingCostCont,
                                                  enabled: weldingCost),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),

                                    SizedBox(height: 10),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Assicurazione C.A.R.',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: carInsurance,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            carInsurance = value;
                                                            txtconCarInsurance!.text = '';
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconCarInsurance,
                                                  enabled: carInsurance),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 17),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label:
                                                          'Assicurazione Postuma',
                                                      fontSize: 12,
                                                      textColor:
                                                          Color(0xff696969),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6.0,
                                                              bottom: 18,
                                                              left: 0),
                                                      child: Switch(
                                                        value: posthumousInsurance,
                                                        activeColor:
                                                            Theme.of(context)
                                                                .primaryColor,
                                                        onChanged:
                                                            (bool value) async {
                                                          _checkForChanges();
                                                          setState(() {
                                                            txtconPosthumousInsurance!.text = '';
                                                            posthumousInsurance = value;
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTextFormField(
                                                  flex: 1,
                                                  label: 'Costo',
                                                  hintText: "",
                                                  controller: txtconPosthumousInsurance,
                                                  enabled: posthumousInsurance),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                )),
                        SizedBox(height: 15),

                        SizedBox(height: 25),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            NarFormLabelWidget(label: progressMessage),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                BaseNewarcButton(
                                  buttonText: "Salva",
                                  onPressed: () async {
                                    if (!_formKey.currentState!.validate()) {
                                      setState(() {
                                        progressMessage =
                                            'Please check your inputs';
                                      });
                                      return;
                                    }
                                    setState(() {
                                      progressMessage =
                                          'Salvataggio in corso...';
                                    });

                                    FixedProperty? _fixedProperty;
                                    try {
                                      _fixedProperty = FixedProperty({
                                        'propertyName':
                                            txtconPropertyName!.text,
                                        'zone': txtconPropertyZoneName!.text,
                                        'city': txtconCity!.text,
                                        'propertyType':
                                            txtconPropertyTypeName!.text,
                                        'areaMQ': txtconAreaMq!.text == ''
                                            ? ''
                                            : double.parse(txtconAreaMq!.text),
                                        'locals': txtconLocals!.text == ''
                                            ? ''
                                            : int.parse(txtconLocals!.text),
                                        'floors': txtconFloors!.text == ''
                                            ? ''
                                            : int.parse(txtconFloors!.text),
                                        'baths': txtconBaths!.text == ''
                                            ? ''
                                            : int.parse(txtconBaths!.text),
                                        'refFirstName':
                                            txtconReferenceFirstName!.text,
                                        'refLastName':
                                            txtconReferenceLastName!.text,
                                        'refPhone': txtconReferencePhone!.text,
                                        'refEmail': txtconReferenceEmail!.text,
                                        'purchaseCost': txtconPropertyPurchase!
                                                    .text ==
                                                ''
                                            ? ''
                                            : double.parse(
                                                txtconPropertyPurchase!.text),
                                        'balance': txtconDeposit!.text == ''
                                            ? (txtconPropertyPurchase!.text ==
                                                    ''
                                                ? 0
                                                : double.parse(
                                                    txtconPropertyPurchase!
                                                        .text))
                                            : double.parse(
                                                    txtconPropertyPurchase!
                                                        .text) -
                                                double.parse(
                                                    txtconDeposit!.text),
                                        'deposit': txtconDeposit!.text == ''
                                            ? ''
                                            : double.parse(txtconDeposit!.text),
                                        'notaryCost':
                                            txtconNotaryCost!.text == ''
                                                ? ''
                                                : double.parse(
                                                    txtconNotaryCost!.text),
                                        'registrationTax':
                                            txtconRegistrationTax!.text == ''
                                                ? ''
                                                : double.parse(
                                                    txtconRegistrationTax!
                                                        .text),
                                        'ape': txtconApe!.text == ''
                                            ? ''
                                            : double.parse(txtconApe!.text),
                                        'condominiumFees':
                                            txtconCondominiumFees!.text == ''
                                                ? ''
                                                : double.parse(
                                                    txtconCondominiumFees!
                                                        .text),
                                        'loadUnloadInstallation':
                                            txtconLoadUnloadInstallation!
                                                        .text ==
                                                    ''
                                                ? ''
                                                : double.parse(
                                                    txtconLoadUnloadInstallation!
                                                        .text),
                                        'registrationTax2':
                                            txtconRegistrationTax2!.text == ''
                                                ? ''
                                                : double.parse(
                                                    txtconRegistrationTax2!
                                                        .text),
                                        'electricalCond':
                                            txtconElectricalCond!.text == ''
                                                ? ''
                                                : double.parse(
                                                    txtconElectricalCond!.text),
                                        'light': txtconLight!.text == ''
                                            ? ''
                                            : double.parse(txtconLight!.text),
                                        'moveGasCont':
                                            txtconMoveGasCont!.text == ''
                                                ? ''
                                                : double.parse(
                                                    txtconMoveGasCont!.text),
                                        'heating': txtconHeating!.text == ''
                                            ? ''
                                            : double.parse(txtconHeating!.text),

                                        'posthumousInsurance': txtconPosthumousInsurance!.text == ''
                                            ? ''
                                            : double.parse(
                                                    txtconPosthumousInsurance!.text),
                                        
                                        'carInsurance': txtconCarInsurance!.text == ''
                                            ? ''
                                            : double.parse(
                                                    txtconCarInsurance!.text),
                                        
                                        'weldingCost': txtconWeldingCostCont!.text == ''
                                            ? ''
                                            : double.parse(
                                                txtconWeldingCostCont!.text),

                                        'purchaseCostPaid':
                                            txtconPropertyPurchase!.text == ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .purchaseCostPaid,
                                        'balancePaid': txtconPropertyPurchase!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .balancePaid,
                                        'depositPaid': txtconDeposit!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .depositPaid,
                                        'notaryCostPaid':
                                            txtconNotaryCost!.text == ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .notaryCostPaid,
                                        'registrationTaxPaid':
                                            txtconRegistrationTax!.text == ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .registrationTaxPaid,
                                        'apePaid': txtconApe!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .apePaid,
                                        'condominiumFeesPaid':
                                            txtconCondominiumFees!.text == ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .condominiumFeesPaid,
                                        'loadUnloadInstallationPaid':
                                            txtconLoadUnloadInstallation!
                                                        .text ==
                                                    ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .loadUnloadInstallationPaid,
                                        'electricalCondPaid':
                                            txtconElectricalCond!.text == ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .electricalCondPaid,
                                        'lightPaid': txtconLight!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .lightPaid,
                                        'moveGasContPaid':
                                            txtconMoveGasCont!.text == ''
                                                ? false
                                                : widget.project!.fixedProperty!
                                                    .moveGasContPaid,
                                        'heatingPaid': txtconHeating!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .heatingPaid,
                                        
                                        'posthumousInsurancePaid': txtconPosthumousInsurance!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .posthumousInsurancePaid,
                                        'carInsurancePaid': txtconCarInsurance!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .carInsurancePaid,
                                        'weldingCostPaid': txtconWeldingCostCont!.text == ''
                                            ? false
                                            : widget.project!.fixedProperty!
                                                .weldingCostPaid,




                                        'purchaseCostPaidDate':
                                            txtconPropertyPurchase!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .purchaseCostPaidDate,
                                        'balancePaidDate':
                                            txtconPropertyPurchase!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .balancePaidDate,
                                        'depositPaidDate':
                                            txtconDeposit!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .depositPaidDate,
                                        'notaryCostPaidDate':
                                            txtconNotaryCost!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .notaryCostPaidDate,
                                        'registrationTaxPaidDate':
                                            txtconRegistrationTax!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .registrationTaxPaidDate,
                                        'apePaidDate': txtconApe!.text == ''
                                            ? 0
                                            : widget.project!.fixedProperty!
                                                .apePaidDate,
                                        'condominiumFeesPaidDate':
                                            txtconCondominiumFees!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .condominiumFeesPaidDate,
                                        'loadUnloadInstallationPaidDate':
                                            txtconLoadUnloadInstallation!
                                                        .text ==
                                                    ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .loadUnloadInstallationPaidDate,
                                        'electricalCondPaidDate':
                                            txtconElectricalCond!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .electricalCondPaidDate,
                                        'lightPaidDate': txtconLight!.text == ''
                                            ? 0
                                            : widget.project!.fixedProperty!
                                                .lightPaidDate,
                                        'moveGasContPaidDate':
                                            txtconMoveGasCont!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .moveGasContPaidDate,
                                        'heatingPaidDate':
                                            txtconHeating!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .heatingPaidDate,
                                        
                                        'posthumousInsurancePaidDate':
                                            txtconPosthumousInsurance!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .posthumousInsurancePaidDate,
                                        'carInsurancePaidDate':
                                            txtconCarInsurance!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .carInsurancePaidDate,
                                        'weldingCostPaidDate':
                                            txtconWeldingCostCont!.text == ''
                                                ? 0
                                                : widget.project!.fixedProperty!
                                                    .weldingCostPaidDate,
                                        "newarcProjectFixedAssetsPropertyPagamento": widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento
                                            ?.map((pagamento) => pagamento.toMap())
                                            .toList(),
                                        'refurbishmentCost': [],
                                        'refurbishmentInstallments': txtconInstallments?.text,
                                        'refurbishmentValue': double.tryParse(txtconRefurbishmentValue?.text ?? "")
                                      });
                                    } catch (e, s) {
                                      print({e, s});
                                    }

                                    widget.project!.fixedProperty =
                                        _fixedProperty;
                                    // widget.fixedProperty = _fixedProperty;


                                    final FirebaseFirestore _db =
                                        FirebaseFirestore.instance;

                                    try {
                                      await _db
                                          .collection(
                                              appConfig.COLLECT_NEWARC_PROJECTS)
                                          .doc(widget.project!.id)
                                          .update(widget.project!.toMap());

                                      for (var pagamento in widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []) {
                                        DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
                                            .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
                                            .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
                                            .get();
                                        if (collectionQuery.exists) {
                                          pagamento.categoryName = collectionQuery.data()?["name"];
                                        }
                                        for (var rate in pagamento.rate ?? []) {
                                          DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                                              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                                              .doc(rate.newarcProjectFixedAssetsPercentageId)
                                              .get();
                                          if (perQuery.exists) {
                                            rate.percentage = perQuery.data()?["percentage"];
                                          }
                                        }
                                      }


                                      setState(() {
                                        widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento = widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento;
                                        widget.isInputChangeDetected![0] = false;
                                        progressMessage = 'Saved!';
                                      });
                                    } catch (e) {
                                      log("ERROR WHILE SAVING PROJECT =====> ${e.toString()}");
                                      setState(() {
                                        progressMessage = 'Error';
                                      });
                                    }
                                  },
                                )
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

}
