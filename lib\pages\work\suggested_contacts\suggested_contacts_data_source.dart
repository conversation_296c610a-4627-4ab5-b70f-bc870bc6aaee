import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/pages/work/suggested_contacts/suggested_contacts_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/tab/common_dropdown_widget.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/widget/UI/link.dart';

class SuggestedContactsWorkDataSource extends DataTableSource {
  final List<RenovationContact> contacts;
  final BuildContext context;
  final List<Map> status;
  final Function() initialFetchContacts;
  final controller = Get.put<SuggestedContactsWorkController>(SuggestedContactsWorkController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  SuggestedContactsWorkDataSource({
    required this.context,
    required this.contacts,
    required this.status,
    required this.initialFetchContacts,
  });

  @override
  DataRow? getRow(int index) {
    if (index < contacts.length){
      RenovationContact cont = contacts[index];
      String commission = cont.agencyCommission != null ? localCurrencyFormatMain.format(cont.agencyCommission) + '€' : '';
      
      String address = '';
      return DataRow2(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
                color: AppColor.borderColor,
                width: 1,
            ),
          ),
        ),
        cells: [
          DataCell(
            FutureBuilder<RenovationContactAddress>(
              future: getRenovationContactAddress(cont.addressInfo!.first ),
              builder: (context, snapshot) {

                /* We don't need to show any special operation status */
                if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                  return SizedBox(height: 0,);
                } 

                address = snapshot.data!.addressInfo!.toShortAddress();

                return NarFormLabelWidget(
                  label: snapshot.data!.addressInfo!.toShortAddress(),
                  fontSize: 12,
                  fontWeight: '800',
                  textAlign: TextAlign.start,
                  textColor: Colors.black,
                );
                
                
              }
            )
          ),
          // Nome e cognome
          DataCell(
            NarFormLabelWidget(
              label: "${cont.personInfo!.name} ${cont.personInfo!.surname}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Telefono
          DataCell(
            NarFormLabelWidget(
              label: "${cont.personInfo!.phone}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Email
          DataCell(
            NarFormLabelWidget(
              label: "${cont.personInfo!.email}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Agenzia
          DataCell(
            FutureBuilder<DocumentSnapshot>(
              future: FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(cont.agencyId).get(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return NarFormLabelWidget(
                    label: 'Loading...',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  );
                }
                if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                  return NarFormLabelWidget(
                    label: '',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  );
                }
                final agencyName = snapshot.data!.get('name') as String?;
                final agencyEmail = snapshot.data!.get('email') as String?;
                final agencyPhone = snapshot.data!.get('phone') as String?;

                return NarLinkWidget(
                  onClick: () {
                    showAgencyDetailDialog(email: agencyEmail, phone: agencyPhone, name: agencyName);
                  },
                  text: agencyName ?? 'Unknown',
                  overflow: TextOverflow.ellipsis,
                  fontSize: 12,
                  fontWeight: '700',
                  textColor: AppColor.black,
                );
              },
            ),
          ),
          // Commissione
          DataCell(
            NarFormLabelWidget(
              label: commission,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Azioni
          DataCell(
            CustomDropdownButton(
              selectedValue: status[status.indexWhere((sta) => sta['value'] == cont.suggestionStatus)]['label'],
              items: status.map((statusMap) {return statusMap['label'] as String;}).toList(),
              hintText: 'Select Status',
              getColor: (selectedStatus) {
                return status[status.indexWhere((sta) => sta['label'] == selectedStatus)]['bgColor'];
              },
              onChanged: (value) async {
                print("Selected value: $value");
                if (value=='Acquisito') {
                  showAcquisitionDataInsertionPopup(cont, address);
                } else {
                  cont.suggestionStatus = status[status.indexWhere((sta) => sta['label'] == value)]['value'];
                  await FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(cont.id).update({'suggestionStatus': cont.suggestionStatus});
                  notifyListeners();
                }
              },
            ),
          ),
          // Data segnalazione
          DataCell(
            NarFormLabelWidget(
              label: DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(cont.created!)).toString(),
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
        ],
      );
    }
    return null;
  }

  Future<void> showAgencyDetailDialog({email, phone, name}) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: name,
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 340,
                child: Row(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 1,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Telefono",
                      controller: TextEditingController(
                        text: phone,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              SizedBox(
                width: 340,
                child: Row(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 1,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Email",
                      controller: TextEditingController(
                        text: email,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  showAcquisitionDataInsertionPopup (RenovationContact contact, String address){
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            title: "Conferma acquisizione",
            buttonText: "Conferma",
            buttonColor: Theme.of(context).primaryColor,
            formErrorMessage: controller.formErrorMessage,
            onPressed: () async {
              contact.suggestionStatus = status[status.indexWhere((sta) => sta['label'] == 'Acquisito')]['value'];
              contact.renovationPrice = double.parse(controller.renovationPriceController.text.replaceAll(".", "").replaceAll(",", "."));
              contact.agencyCommission = double.parse(controller.agencyCommissionController.text.replaceAll(".", "").replaceAll(",", "."));
              
              await FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                                              .doc(contact.id)
                                              .update({
                                                'suggestionStatus': contact.suggestionStatus,
                                                'renovationPrice': contact.renovationPrice,
                                                'agencyCommission': contact.agencyCommission,
                                              });
              notifyListeners();
              controller.clearPopupcontrollers();
              Navigator.of(context).pop;
            },
            column: Container(
              width: 250,
              height: 250,
              child: ListView(
                padding: EdgeInsets.symmetric(vertical: 18),
                children: [
                  NarFormLabelWidget(
                    label: address,
                    fontSize: 15,
                    fontWeight: '800',
                    textAlign: TextAlign.center,
                    textColor: Colors.black,
                  ),
                  SizedBox(height: 30,),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      CustomTextFormField(
                        label: "Importo lavori",
                        // suffixIcon: Icon(Icons.euro, size: 18, color: AppColor.drawerIconButtonColor,),
                        isMoney: true,
                        validator: (value) {
                          if (value == '') {
                            return 'Obbligatorio';
                          }
                          return null;
                        },
                        controller: controller.renovationPriceController,
                        //inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),],
                      ),
                    ],
                  ),
                  SizedBox(height: 15,),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      CustomTextFormField(
                        label: "Importo commissione",
                        // suffixIcon: Icon(Icons.euro, size: 18, color: AppColor.drawerIconButtonColor,),
                        isMoney: true,
                        validator: (value) {
                          if (value == '') {
                            return 'Obbligatorio';
                          }
                          return null;
                        },
                        controller: controller.agencyCommissionController,
                        //inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),],
                      ),
                    ],
                  )
                ]
              ),
            ),
          )
        );
      }
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => contacts.length;

  @override
  int get selectedRowCount => 0;
}

