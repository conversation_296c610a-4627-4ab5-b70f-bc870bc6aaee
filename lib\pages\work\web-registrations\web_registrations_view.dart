import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/web-registrations/web_registrations_controller.dart';
import 'package:newarc_platform/pages/work/web-registrations/web_registrations_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class WebRegistrationsView extends StatefulWidget {
  
  final responsive;

  const WebRegistrationsView({
    Key? key,
    required this.responsive,
  }) : super(key: key);

  @override
  State<WebRegistrationsView> createState() => _WebRegistrationsViewState();
}

class _WebRegistrationsViewState extends State<WebRegistrationsView> {
  final controller = Get.put<WebRegistrationsController>(WebRegistrationsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.clearFilter();
    controller.selectedStatus = controller.testList.first;
    initialFetchContacts();
    super.initState();
  }


  
  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: IconTheme.merge(
          data: const IconThemeData(opacity: 0.54),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                style: TextStyle(
                  fontFamily: '',
                  fontSize: 12.0,
                  color: Colors.black.withOpacity(0.54),
                ),
              ),
              SizedBox(width: 32.0),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  if (controller.disablePreviousButton == true) return;
                  if (controller.loadingContacts.value == true) return;
                  fetchPrevContacts();
                },
                padding: EdgeInsets.zero,
              ),
              SizedBox(width: 24.0),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (controller.disableNextButton == true) return;
                  if (controller.loadingContacts.value == true) return;

                  fetchNextContacts();
                },
              ),
              SizedBox(width: 14.0),

              // TextButton(
              //   child: Icon(Icons.refresh, size: 20, color: disableNextButton ? Colors.grey : Colors.black),
              //   onPressed: () {
              //     cacheFirestore.clear();
              //     initialFetchContacts();
              //   },
              //   style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
              // )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> initialFetchContacts({bool force = false,bool reloadAll = false}) async {
    if (controller.users.isNotEmpty && !force && !reloadAll) return;

    //TODO Rimuovere la collectionSnapshotCounterQuery, ha duplicato tutto555
    controller.pageCounter = 1;

    setState(() {
      controller.users = [];
      controller.loadingContacts.value = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
      .where('role', isEqualTo: 'web-client')
      .where('type', isEqualTo: 'web-client');
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
      .where('role', isEqualTo: 'web-client')
      .where('type', isEqualTo: 'web-client');

      //print({'master filter', filters.length, filters});
      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          } else if (controller.filters[i]['search'] == 'like') {
            collectionSnapshotQuery = collectionSnapshotQuery
                .where(controller.filters[i]['field'], isGreaterThanOrEqualTo: controller.filters[i]['value'])
                .where(controller.filters[i]['field'], isLessThanOrEqualTo: controller.filters[i]['value'] + "\uf7ff");
            collectionSnapshotCounterQuery
                .where(controller.filters[i]['field'], isGreaterThanOrEqualTo: controller.filters[i]['value'])
                .where(controller.filters[i]['field'], isLessThanOrEqualTo: controller.filters[i]['value'] + "\uf7ff");
          }
        }
      }

      // if (reloadAll) {
      //   collectionSnapshot = await collectionSnapshotQuery.get();
      // } else {
      //   collectionSnapshot = await collectionSnapshotQuery.orderBy('insertion_timestamp', descending: true).limit(controller.recordsPerPage).get();
      // }
      collectionSnapshot = await collectionSnapshotQuery.get();


      //await collectionSnapshotQuery.limit(recordsPerPage).get();

      collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();

      //totalRecords = collectionSnapshot.docs.length;
      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;


      await generateContacts(collectionSnapshot);


      // setState(() {
      //   controller.loadingContacts.value = false;
      // });
    } catch (e,s) {
      log(" Error in initialFetchContacts : $e");
      log("Stacktrace in initialFetchContacts: $s");
      setState(() {
        controller.loadingContacts.value = false;
      });
      // print(e.toString());
    }
  }

  fetchNextContacts() async {
    setState(() {
      controller.loadingContacts.value = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
        .where('role', isEqualTo: 'web-client')
        .where('type', isEqualTo: 'web-client');

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .get();
        // collectionSnapshot = await collectionSnapshotQuery.limit(recordsPerPage).startAfterDocument(documentList[documentList.length - 1]).get();
      }

      // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      controller.documentList = collectionSnapshot.docs;

      // documentList.addAll(newDocumentList);

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts.value = false;
      });
      // print(e.toString());
      // movieController.sink.addError(e);
    }
  }

  fetchPrevContacts() async {
    setState(() {
      controller.loadingContacts.value = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS)
        .where('role', isEqualTo: 'web-client')
        .where('type', isEqualTo: 'web-client');

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertion_timestamp', descending: true)
            .limit(controller.recordsPerPage)
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .get();
        
      }

      controller.documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts.value = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateContacts(collectionSnapshot) async {
    
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<NewarcUser> _users = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = NewarcUser.fromDocument(element.data(), element.id);
        _users.add(_tmp);
      }catch (e, stacktrace) {
        log("Error processing doc ${element.id}: $e");
        log("Stacktrace: $stacktrace");
      }
    }

    
    setState(() {
      controller.users = _users;
      controller.displayUsers = _users;
      controller.loadingContacts.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Registrazioni sito',
                fontSize: 19,

                fontWeight: '700',
                textColor: Colors.black,
              ),
            ],
          ),
          _filter(),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColor.white,
              border: Border.all(width: 1.5, color: AppColor.borderColor),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingContacts.value ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          hidePaginator: true,
                          onPageChanged: (val) {
                            print("page : $val");
                          },
                          columns: [
                            DataColumn2(
                              label: Text(
                                'Nome e Cognome',
                              ),
                              size: ColumnSize.L
                            ),
                            DataColumn2(
                              label: Text(
                                'Email',
                              ),
                              size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Telefono', 
                              ),
                              size: ColumnSize.S
                            ),
                            
                          ],
                          isHasDecoration: false,
                          source: WebRegistrationsDataSource(
                            displayUsers: controller.displayUsers,
                            context: context,
                            initialFetchContacts: initialFetchContacts,
                            newarcUser: controller.newarcUser,
                          ),
                        ),
                      ),
                      if (controller.loadingContacts.value)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: dataTablePagination(),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      isFilterHide: true,
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<NewarcUser> filtered = controller.users.where((contact) {
              final email = contact.email!.toLowerCase();
              final firstName = contact.firstName!.toLowerCase();
              final lastName = contact.lastName!.toLowerCase();
              return email.contains(searchQuery!.toLowerCase()) || firstName.contains(searchQuery.toLowerCase()) || lastName.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.displayUsers = filtered;
            });
          }
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayUsers = controller.users;
          });
        }
      },
      searchHintText: "Cerca per name or email..",
      suffixIconOnTap: ()async{
        
        await initialFetchContacts(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<NewarcUser> filtered = controller.users.where((contact) {
          
              final email = contact.email!.toLowerCase();
              final firstName = contact.firstName!.toLowerCase();
              final lastName = contact.lastName!.toLowerCase();
            
              return email.contains(controller.searchTextController.text.toLowerCase()) || firstName.contains(controller.searchTextController.text.toLowerCase()) || lastName.contains(controller.searchTextController.text.toLowerCase());
          }).toList();
          setState(() {
            controller.displayUsers = filtered;
          });
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayUsers = controller.users;
          });
        }
      },
      selectedFilters: [],
      textEditingControllers: [],
      filterFields: [],
      onSubmit: () async {
        await initialFetchContacts(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        controller.filters.clear();
        await initialFetchContacts(force: true);
      },
    );
  }

  //Why not a stream?
  Future getCommentCount(String firebaseId) async {
    QuerySnapshot<Map<String, dynamic>>? collectionSnapshot;
    collectionSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS).where('contactId', isEqualTo: firebaseId).orderBy('date', descending: true).get();

    return collectionSnapshot.docs.length;
  }
}