import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/agency/agency_comment_popup.dart';
import 'package:newarc_platform/widget/agency/assegna_immobile_popup.dart';

class WebRegistrationsDataSource extends DataTableSource {
  List<NewarcUser> displayUsers;
  BuildContext context;
  Function()? initialFetchContacts;
  NewarcUser? newarcUser;

  WebRegistrationsDataSource({
    required this.displayUsers,
    required this.context,
    this.initialFetchContacts,
    this.newarcUser,
  });

  @override
  DataRow? getRow(int index) {
    if (index < displayUsers.length) {
      NewarcUser user = displayUsers[index];
      

      return DataRow(
        cells: [
          DataCell(
            NarLinkWidget(
              text: '${user.firstName} ${user.lastName}',
              textColor: Colors.black,
              fontWeight: '700',
              overflow: TextOverflow.ellipsis,
              fontSize: 12,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: user.email,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: '${user.phoneCode} ${user.phone}',
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          )
          
        ],
      );
    }

    return null;
  }

  

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayUsers.length;

  @override
  int get selectedRowCount => 0;
}
