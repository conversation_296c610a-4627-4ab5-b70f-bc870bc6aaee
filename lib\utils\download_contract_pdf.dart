
import 'dart:convert';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;
import 'package:http/http.dart' show get;
import '../classes/newarcProjectFixedAssetsPropertyPagamento.dart';
import '../classes/renovationQuotation.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;


getNetworkImage(url) async {
  if (url == '') {
    final Uint8List coverLogoData = await _loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);
    return coverLogo;
  }
  var response = await get(Uri.parse(url));
  var data = response.bodyBytes;
  return pw.MemoryImage(data);
}

Future<Uint8List> _loadImage(String path) async {
  final ByteData data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}

String numberToAlphabet(int number) {
  if (number <= 0) return "";

  String result = '';
  while (number > 0) {
    number--;
    int remainder = number % 26;
    result = String.fromCharCode(65 + remainder) + result;
    number = number ~/ 26;
  }
  return result;
}

fetchPagamentoCategory(RenovationQuotation quotation)async{

}

Future<Uint8List?> downloadContractPDF({required RenovationContract  contract, required RenovationQuotation quotation,required isPDFDownload})async{
  try{


    NewarcProjectPagamento? pagamento = quotation.pagamento?.firstWhere((val){
      return val.categoryName == "Ristrutturazione";
    });

    double safetyCharges = 0;

    quotation.renovationActivity?.forEach((val){
      if(val.category == "C - Lavori interni"){
        val.activity?.forEach((act){
          if(act.subCategory == "SI - Sicurezza"){
            double activityDiscountAmount = (double.tryParse(act.activityDiscountAmount.toString()) ?? 0.0);
            safetyCharges += (((act.unitPrice ?? 0) * (act.quantity ?? 0)) - activityDiscountAmount);
          }
        });
      }
    });

    NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT',symbol: "", decimalDigits: 2);

    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ByteData fontLightData = await rootBundle.load('assets/fonts/Raleway-Regular.ttf');
    final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');

    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());
    final ralewayLight = pw.Font.ttf(fontLightData.buffer.asByteData());

    final Uint8List pdfLogoData = await _loadImage('assets/rq-pdf-cover-logo.png');
    final pdfLogo = pw.MemoryImage(pdfLogoData);

    final Uint8List signAndStampData = await _loadImage('assets/sign-and-stamp.jpg');
    final signAndStamp = pw.MemoryImage(signAndStampData);

    final pdf = pw.Document();

    List<pw.Widget> contactPage = [];

    pw.Widget pageFooter(String pagenumber){
      return pw.Container(
        child: pw.Row(
          children: [
            pw.Image(pdfLogo, height: 20,width: 52),
            pw.SizedBox(width: 17),
            pw.Text(
              "Newarc Srl - Sede Operativa: Corso Ferrucci, 36, 10138, Torino, IT - Sede Legale: Via V.Emanuele II, 29, 10023, Chieri, TO, IT \nP.Iva 12533550013 - <EMAIL> - www.newarc.it - Tel 011 026 38 50",
              textAlign: pw.TextAlign.center,
              style: pw.TextStyle(
                font: ralewayLight,
                fontSize: 7,
                color: PdfColors.black,
              )
            ),
            pw.Expanded(
              child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                        pagenumber,
                        textAlign: pw.TextAlign.center,
                        style: pw.TextStyle(
                          font: ralewayBold,
                          fontSize: 10,
                          color: PdfColors.black,
                        )
                    ),
                  ]
              )
            )
          ]
        )
      );
    }

    pw.Widget _pointText(String text){
      return pw.Text(
          text,
          style: pw.TextStyle(
              font: ralewayLight,
              fontSize: 8,
              color: PdfColors.black,
              lineSpacing: 3,
              height: 13
          )
      );
    }

    pw.Widget _headerText(String text){
      return pw.Text(
          text,
          style: pw.TextStyle(
              font: ralewayBold,
              fontSize: 8,
              color: PdfColors.black,
              lineSpacing: 3,
              height: 13
          )
      );
    }

    pw.Widget _mainHeaderText(String text){
      return pw.Text(
          text,
          style: pw.TextStyle(
              font: ralewayBold,
              fontSize: 12,
              color: PdfColors.black
          )
      );
    }

    //------ First Page
    contactPage.add(
      pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(height: 11),
          pw.Center(
            child: pw.Image(pdfLogo, height: 44,width: 115)
          ),
          pw.SizedBox(height: 36),
          pw.Center(
            child: pw.Text(
                "CONTRATTO D’APPALTO",
                style: pw.TextStyle(
                    font: ralewayBold,
                    fontSize: 18,
                    color: PdfColors.black
                )
            )
          ),
          pw.SizedBox(height: 20),
          pw.Center(child: _mainHeaderText("TRA")),
          pw.SizedBox(height: 20),
          pw.Text(
              "NEWARC srl, con sede legale in Chieri (TO), via Vittorio Emanuele II, n. 29, P. IVA 12533550013, iscritta alla CCIAA di\nTorino REA n. TO - 1297236, in persona del legale rappresentante Presidente del CdA dott. Edoardo Tabasso, d’ora\n in avanti denominata Newarc srl o l’Appaltatore",
              style: pw.TextStyle(
                  font: ralewayMedium,
                  fontSize: 10,
                  color: PdfColors.black,
                  lineSpacing: 3,
                  height: 13
              )
          ),
          pw.SizedBox(height: 20),
          pw.Center(child: _mainHeaderText("E")),
          pw.SizedBox(height: 20),
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        "${contract.firstName ?? ""} ${contract.lastName ?? ""}",
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 10,
                            color: PdfColors.black,
                            lineSpacing: 3,
                            height: 13
                        )
                    ),
                    pw.Text(
                        ".......................................................................................",
                        tightBounds: true,
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 10,
                            color: PdfColors.black,
                        )
                    ),
                  ]
              ),
              pw.Text(
                  "  nato/a a  ",
                  style: pw.TextStyle(
                      font: ralewayMedium,
                      fontSize: 10,
                      color: PdfColors.black,
                      lineSpacing: 3,
                      height: 13
                  )
              ),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        contract.baseAddressInfo?.city ?? "",
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 10,
                            color: PdfColors.black,
                            lineSpacing: 3,
                            height: 13
                        )
                    ),
                    pw.Text(
                        ".......................................................................................................",
                        tightBounds: true,
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 10,
                            color: PdfColors.black,
                        )
                    ),
                  ]
              ),
              pw.Text(
                  "  il  ",
                  style: pw.TextStyle(
                      font: ralewayMedium,
                      fontSize: 10,
                      color: PdfColors.black,
                      lineSpacing: 3,
                      height: 13
                  )
              ),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        contract.dateOfBirth != null ? getFormattedDate(contract.dateOfBirth!) : "",
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 10,
                            color: PdfColors.black,
                            lineSpacing: 3,
                            height: 13
                        )
                    ),
                    pw.Text(
                        "...........................................",
                        tightBounds: true,
                        style: pw.TextStyle(
                            font: ralewayMedium,
                            fontSize: 10,
                            color: PdfColors.black
                        )
                    ),
                  ]
              ),
            ]
          ),
          pw.SizedBox(height: 2),
          pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                    "residente a  ",
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColors.black,
                        lineSpacing: 3,
                        height: 13
                    )
                ),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.baseAddressInfo?.city ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 10,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "..............................................................................................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 10,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
                pw.Text(
                    " in ",
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColors.black,
                        lineSpacing: 3,
                        height: 13
                    )
                ),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.baseAddressInfo?.toShortAddress() ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 10,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "......................................................................................................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 10,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
              ]
          ),
          pw.SizedBox(height: 2),
          pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                    "con CF  ",
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColors.black,
                        lineSpacing: 3,
                        height: 13
                    )
                ),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.taxIdCode ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 10,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "...........................................................................................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 10,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
                pw.Text(
                    "  d’ora in avanti denominato Committente.",
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColors.black,
                        lineSpacing: 3,
                        height: 13
                    )
                ),
              ]
          ),
          pw.SizedBox(height: 20),
          pw.Center(child: _mainHeaderText("PREMESSO CHE")),
          pw.SizedBox(height: 20),
          _headerText("Il committente dichiara di:"),
          pw.SizedBox(height: 5),
          _pointText("(a) essere proprietario o comunque di avere la piena disponibilità (giuridica e di fatto) del seguente immobile:"),
          pw.SizedBox(height: 3),
          pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _pointText("Indirizzo  "),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          quotation.renovationContactAddress?.addressInfo?.fullAddress ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 8,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "...................................................................................................................................................................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 8,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
              ]
          ),
          pw.SizedBox(height: 3),
          pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _pointText("Piano  "),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          quotation.floor ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 8,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          ".......................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 8,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
                _pointText("  Sezione urbana  "),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.urbanSection ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 8,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "......................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 8,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
                _pointText("  Foglio  "),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.paper ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 8,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "..............................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 8,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
                _pointText("  Particella  "),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.particle ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 8,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          "............................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 8,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),
                _pointText("  Subalterno  "),
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                          contract.subaltern ?? "",
                          style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 8,
                              color: PdfColors.black,
                              lineSpacing: 3,
                              height: 13
                          )
                      ),
                      pw.Text(
                          ".................................",
                          tightBounds: true,
                          style: pw.TextStyle(
                              font: ralewayMedium,
                              fontSize: 8,
                              color: PdfColors.black
                          )
                      ),
                    ]
                ),

              ]
          ),
          pw.SizedBox(height: 20),

          _pointText("(b) voler far realizzare opere di manutenzione ordinaria / manutenzione straordinaria / ristrutturazione edilizia dell’immobile stesso e in\nparticolare quelle previste nei progetti, elaborati tecnici, disegni e nel capitolato allegati a questo contratto, tutti forniti dal Committente\ntramite il tecnico incaricato;\n(c) avere la piena disponibilità del suddetto immobile;\n(d) aver redatto il progetto in conformità alla normativa urbanistico-edilizia e tecnica;\n(e) avere chiesto ed ottenuto tutte le autorizzazioni, istanze, pareri nulla osta, titoli abilitativi comunque denominati necessari, secondo le\nvigenti disposizioni di legge, per eseguire i lavori di cui al presente contratto impegnandosi irrevocabilmente a consegnare tutta la relativa\ndocumentazione alla ditta Newarc srl entro e non oltre il termine per l’inizio del lavori;\n(f) voler incaricare Newarc srl dell’esecuzione delle opere;"),


          pw.SizedBox(height: 20),
          _headerText("Newarc dichiara di:"),
          pw.SizedBox(height: 5),
          _pointText("(g) essere una società che si occupa di lavori edili dotata dei necessari requisiti di legge idonea sotto il profilo tecnico- professionale a\nsvolgere l’esecuzione delle opere da realizzare con gestione a proprio rischio e con organizzazione dei mezzi necessari;\n(h) non avvalersi di dipendenti e che, qualora per l’esecuzione dei lavori edili se ne dovesse avvalere, applicherà nei loro confronti, come\ndatore di lavoro, uno dei contratti collettivi nazionali di lavoro tra quelli designati dai codici CNEL F012, F015 e/o F018; (i) in caso di lavori\nedili oltre i 70 mila euro, impegnarsi altresì a:\n• consegnare al committente un estratto dei contratti di sub- affidamento dei lavori, al fine di consentirgli di verificare l’avvenuta indicazione\ndel relativo contratto collettivo di lavoro applicato ai dipendenti, ove presenti;\n• consegnare al committente una dichiarazione sostitutiva di notorietà del subappaltatore attestante il contratto collettivo di lavoro applicato senza riporto dei relativi estremi sulle fatture emesse;\n(l) avere le seguenti posizioni previdenziali ed assicurative:\nINPS 8148846962 - INAIL 96211378/95 ;\n(m) di essere perfettamente a conoscenza, per averne presa visione diretta sul posto, delle condizioni generali e particolari nell’ambito delle\nquali le proprie prestazioni debbono essere effettuate, delle modalità esecutive, difficoltà, oneri e rischi inerenti l’esecuzione dei lavori ed in\ngenere di tutte le circostanze che possono avere direttamente ed indirettamente influenza sullo svolgimento dei lavori, sulle misure\ngenerali per la protezione della salute e per la sicurezza dei lavoratori nonché sui relativi costi;\n(n) voler accettare l’incarico del Committente"),

        ]
      )
    );
    contactPage.add(pw.NewPage());

    //------ Second Page
    contactPage.add(
        pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              pw.Center(child: _mainHeaderText("SI CONVIENE E STIPULA QUANTO SEGUE")),
              pw.SizedBox(height: 20),
              _headerText("1. Premessa"),
              _pointText("La premessa vale patto tra le parti."),
              pw.SizedBox(height: 20),
              _headerText("APPALTO"),
              pw.SizedBox(height: 20),
              _headerText("2. Appalto"),
              pw.SizedBox(height: 5),
              _pointText("A) Il Committente affida in appalto Newarc srl, che accetta l’esecuzione delle opere previste negli allegati a questo contratto (capitolato ed\nelaborati grafici), tutti sottoscritti dalle parti. B) L’appalto è da intendersi a misura e non a corpo. C) Sono esclusi dall’appalto le prestazioni\nd’opera professionale dirette alla predisposizione degli elaborati progettuali, alla presentazione di pratiche amministrative e allo\nsvolgimento della direzione dei lavori."),

              pw.SizedBox(height: 20),
              _headerText("3. Subappalto"),
              pw.SizedBox(height: 5),
              _pointText("Il Committente autorizza espressamente Newarc srl a subappaltare ad imprese terze, in tutto o in parte, le opere commissionate di cui\nall’allegato preventivo, restando Newarc srl responsabile verso il Committente di monitorare e vigilare la regolarità di quanto oggetto di\nsubappalto."),

              pw.SizedBox(height: 20),
              _headerText("4. Inizio dei lavori"),
              pw.SizedBox(height: 5),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("A) I lavori avranno inizio il  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.startWorkDate != null ? getFormattedDate(contract.startWorkDate) : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "............................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 10,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  a condizione che entro tale data: a) siano trascorsi almeno 15 giorni dall’incasso del 1° acconto"),
                  ]
              ),
              pw.SizedBox(height: 3),
              _pointText("lavori; b) Newarc srl sia stato immesso dal Committente nella detenzione dell’immobile libero e sgombro da persone e cose; c) il\nCommittente tramite il suo direttore dei lavori abbia consegnato a Newarc srl tutte le autorizzazioni amministrative necessarie nonché il\nprogetto esecutivo delle opere da realizzare approvato dal Committente stesso; d) qualora l’immobile oggetto di ristrutturazione sia ubicato\nin aree cittadine alle quali è possibile accedere con veicoli e mezzi solo previa autorizzazione delle competenti autorità locali, tale\nautorizzazione sia stata concessa tempestivamente. B) La suddetta data di inizio dei lavori e la conseguente data di consegna delle opere\nverranno differite qualora, entro la data stessa, il Committente non abbia adempiuto a tutto quanto sopra previsto alle lettere da a) a d)."),


              pw.SizedBox(height: 20),
              _headerText("5. Consegna dei lavori"),
              pw.SizedBox(height: 5),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("A) La consegna dei lavori ultimati avverrà entro  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.requiredWorkingDays != null ? contract.requiredWorkingDays.toString() : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              ".....................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 10,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  giorni lavorativi dalla data di inizio dei lavori stessi, e quindi entro il  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.endWorkDate != null ? getFormattedDate(contract.endWorkDate) : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              ".....................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 10,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  salvo")
                  ]
              ),
              pw.SizedBox(height: 3),
              _pointText("quanto sopra previsto all’art. 5, lett. B), richieste integrative di lavori come da artt. 21 e 22 seguenti, sospensioni previste nel presente\ncontratto, sospensioni necessarie o concordate tra le parti, le ferie del mese d’agosto, le festività o altri eventi non imputabili alla Newarc srl.\nB) In ogni caso di sospensione il termine ultimo di consegna sarà prorogato di un tempo pari alla sospensione stessa"),

              pw.SizedBox(height: 20),
              _headerText("6. Pagamento del prezzo e SAL"),
              pw.SizedBox(height: 5),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("Il prezzo complessivo dell’appalto è convenuto in euro  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              pagamento?.total != null ? localCurrencyFormatMain.format(pagamento!.total) : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "......................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  (oltre IVA), di cui euro  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              safetyCharges != 0 ? localCurrencyFormatMain.format(safetyCharges) : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "..........................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  per oneri della")
                  ]
              ),
              pw.SizedBox(height: 3),
              _pointText("sicurezza. Tale prezzo riguarda solo ed esclusivamente le Sezioni del preventivo allegato. Eventuali altre Sezioni saranno oggetto di separati preventivi di fornitura e/o servizi. Non è ammessa alcuna variazione del suddetto prezzo così concordato se non per variazioni in corso d’opera ai sensi degli artt. 20 o 21."),
              pw.SizedBox(height: 3),
              _pointText("A) Il Committente si impegna a corrispondere il suddetto prezzo concordato alle seguenti scadenze e modalità:"),
              pw.SizedBox(height: 5),

              ...?pagamento?.rate?.map((rate){
                return pw.Padding(
                  padding: pw.EdgeInsets.only(bottom: 5),
                  child: pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        _pointText("${numberToAlphabet(rate.index!)}. Il  "),
                        pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                  rate.percentage.toString(),
                                  style: pw.TextStyle(
                                      font: ralewayBold,
                                      fontSize: 8,
                                      color: PdfColors.black
                                  )
                              ),
                              pw.Text(
                                  "................",
                                  tightBounds: true,
                                  style: pw.TextStyle(
                                      font: ralewayMedium,
                                      fontSize: 8,
                                      color: PdfColors.black
                                  )
                              ),
                            ]
                        ),
                        _pointText("  % pari a €  "),
                        pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                  localCurrencyFormatMain.format(rate.rate),
                                  style: pw.TextStyle(
                                      font: ralewayBold,
                                      fontSize: 8,
                                      color: PdfColors.black,
                                      lineSpacing: 0,
                                      height: 0
                                  )
                              ),
                              pw.Text(
                                  "............................",
                                  tightBounds: true,
                                  style: pw.TextStyle(
                                      font: ralewayMedium,
                                      fontSize: 8,
                                      color: PdfColors.black,
                                      lineSpacing: 0,
                                      height: 0
                                  )
                              ),
                            ]
                        ),
                        _pointText("  + iva entro 5 giorni dal SAL  "),
                        pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                  rate.description ?? "",
                                  style: pw.TextStyle(
                                      font: ralewayBold,
                                      fontSize: 8,
                                      color: PdfColors.black
                                  )
                              ),
                              pw.Text(
                                  "...............................................................................................................................................................................",
                                  tightBounds: true,
                                  style: pw.TextStyle(
                                      font: ralewayMedium,
                                      fontSize: 8,
                                      color: PdfColors.black
                                  )
                              ),
                            ]
                        ),
                      ]
                  )
                );
              }),
              pw.SizedBox(height: 5),
              _pointText("I SAL dovranno obbligatoriamente essere asseverati dal Direttore Lavori incaricato dal Committente e comunicati a Newarc. In caso di\nmancata Direzione Lavori, Newarc valuterà internamente i SAL i quali verranno condivisi e comunicati al committente."),

            ]
        )
    );
    contactPage.add(pw.NewPage());

    //------ Third Page
    contactPage.add(
        pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              _headerText("7. Verifica finale e verbale di consegna lavori"),
              pw.SizedBox(height: 5),
              _pointText("A) La verifica finale di cui all’art. 1665 cod. civ. dovrà essere effettuata entro 7 giorni dalla ultimazione dei lavori. In caso di esito positivo verrà\nredatto un verbale che conterrà anche l’accettazione dei lavori senza riserve con contestuale consegna del bene e delle chiavi. B) Qualora il\nCommittente, senza giustificato motivo, non proceda alle predette verifiche entro il suddetto termine, l’opera si intenderà accettata."),

              pw.SizedBox(height: 20),

              _headerText("8. Certificazioni impianti"),
              pw.SizedBox(height: 5),
              _pointText("Il rilascio delle certificazioni di cui al D.M. 37/2008 avverrà esclusivamente:\na) a seguito del rifacimento integrale degli impianti o, in caso di rifacimento parziale, a seguito della consegna da parte del Committente\ndella documentazione inerente ed attestante lo stato preesistente degli impianti stessi e comunque solo nei casi previsti dalla legge;\nb) ad opere ultimate e subordinatamente al pagamento di tutte le somme dovute dal Committente in forza del presente contratto;\nc) entro 30 giorni dall’avvenuto pagamento del saldo finale."),

              pw.SizedBox(height: 20),
              _headerText("9. Garanzia e interventi in garanzia"),
              pw.SizedBox(height: 5),
              _pointText("La garanzia dei lavori è come di legge. Qualora il Committente richieda interventi in garanzia nei termini di legge, Newarc srl sarà tenuto ad\nintervenire, ma, se a seguito di sopralluogo per tale intervento, emergesse che i vizi/difetti lamentati dal Committente non fossero\nriconducibili a responsabilità della Newarc srl, questi non sarà tenuto ad effettuare alcun intervento ed il Committente sarà tenuto a\ncorrispondere alla Newarc srl l’importo di € 150,00 oltre IVA per il sopralluogo."),

              pw.SizedBox(height: 36),
              pw.Center(child: _mainHeaderText("OBBLIGHI E DIVIETI DELLE PARTI")),
              pw.SizedBox(height: 20),

              _headerText("10. Collaborazione delle parti"),
              pw.SizedBox(height: 5),
              pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Container(
                    height: 12,
                    width: 12,
                    alignment: pw.Alignment.center,
                    decoration: pw.BoxDecoration(
                      color: PdfColor.fromHex("#EBEBEB"),
                      border: pw.Border.all(color: PdfColor.fromHex("#B6B6B6"),width: 0.5)
                    ),
                    child: pw.Text(contract.isExternalWorkManager ?? false ? "x" : "",style: pw.TextStyle(fontSize: 12,color: PdfColors.black,font: ralewayBold),tightBounds: true)
                  ),
                  _headerText(" Il Committente nomina un Direttore dei Lavori"),

                ]
              ),
              pw.SizedBox(height: 3),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("Il Committente: a) dichiara di avere affidato la Direzione dei Lavori a  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? "${contract.constructionManagerInfo?.name ?? ""} ${contract.constructionManagerInfo?.surname ?? ""}" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "..........................................................................................................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                  ]
              ),
              pw.SizedBox(height: 3),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("con domicilio in  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.addressInfo?.toFullAddress() ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "................................................................................................................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  città  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.addressInfo?.city ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "................................................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                  ]
              ),
              pw.SizedBox(height: 3),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("tel  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.phone ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "..............................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  email/pec  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.email ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "...................................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  P.iva  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.VATNumber ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "................................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                  ]
              ),
              pw.SizedBox(height: 3),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("iscritto nell’Albo dei/degli  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.register ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              ".......................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  di  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.cityRegister ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "...............................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  col n  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.isExternalWorkManager ?? false ? contract.constructionManagerInfo?.registrationNumber ?? "" : "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "..................................................................................",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black,

                              )
                          ),
                        ]
                    ),
                  ]
              ),
              pw.SizedBox(height: 3),
              _pointText("; b) riconosce e accetta il di lui operato, quale suo rappresentante per tutto quanto attiene l’esecuzione dei lavori oggetto dell’appalto e per\nquant’altro previsto nel presente contratto; c) si impegna a comunicare tempestivamente a mezzo raccomandata a.r. o Pec eventuali\nmodificazioni all’incarico che dovesse intervenire; in difetto, tali modifiche non saranno efficaci nei confronti della Newarc srl."),


              pw.SizedBox(height: 20),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Container(
                        height: 12,
                        width: 12,
                        alignment: pw.Alignment.center,
                        decoration: pw.BoxDecoration(
                            color: PdfColor.fromHex("#EBEBEB"),
                            border: pw.Border.all(color: PdfColor.fromHex("#B6B6B6"),width: 0.5)
                        ),
                        child: pw.Text(!(contract.isExternalWorkManager ?? false) ? "x" : "",style: pw.TextStyle(fontSize: 12,color: PdfColors.black,font: ralewayBold),tightBounds: true)
                    ),
                    _headerText(" Il Committente non nomina un Direttore dei Lavori"),

                  ]
              ),
              pw.SizedBox(height: 3),
              _pointText("Il Committente dichiara di essere stato informato della facoltà di nominare un Direttore dei Lavori esterno per garantire un controllo tecnico\nimparziale e indipendente sul cantiere. Nonostante ciò, dichiara di rinunciare a tale nomina e di affidare il controllo tecnico e operativo di\ncantiere a personale interno di Newarc Srl, consapevole dei potenziali conflitti di interesse derivanti da tale scelta."),


              pw.SizedBox(height: 20),
              _headerText("11. Obblighi di Newarc srl ed esonero di responsabilità"),
              pw.SizedBox(height: 5),
              _pointText("Newarc srl si impegna:"),
              pw.SizedBox(height: 3),
              _pointText("a) a far realizzare dalla ditta o dagli artigiani in subappalto l’impianto di cantiere e predisporre e mantenere gli opportuni presidi a tutela\ndella sicurezza dei lavoratori ai sensi delle attuali disposizioni di legge;"),
              pw.SizedBox(height: 3),
              _pointText("b) a vigilare i lavori e a far eseguire dalla ditta o dagli artigiani in subappalto le opere a perfetta regola d’arte, con organizzazione dei mezzi\nnecessari e maestranze dotate di adeguata capacità tecnica, e gestione a proprio rischio;"),
              pw.SizedBox(height: 3),
              _pointText("Newarc srl non risponde:"),
              pw.SizedBox(height: 3),
              _pointText("c) dei danni a cose e a persone provocati dalle ditte o dagli artigiani in subappalto durante lo svolgimento dei lavori."),
              pw.SizedBox(height: 3),
              _pointText("d) dei danni a cose che non siano state rimosse ai sensi dell’art. 5, lett.A), b) dal Committente stesso o che questi abbia introdotto o fatto\nintrodurre successivamente in cantiere."),

              pw.SizedBox(height: 20),
              _headerText("12. Oneri del Committente"),
              pw.SizedBox(height: 5),
              _pointText("Sono a carico del Committente, salvo diversa ed espressa pattuizione di affidamento dell’incarico a Newarc, oltre agli obblighi previsti in altri\narticoli del presente contratto: a) la predisposizione e la consegna degli elaborati progettuali necessari per l’esecuzione delle opere, tutti gli\nadempimenti di carattere amministrativo, tecnico e i relativi oneri/costi ai fini dell’ottenimento dei permessi/autorizzazioni necessari per il\ntempestivo inizio delle opere; b) la tempestiva integrazione, se necessario, degli allegati progettuali; c) la fornitura a Newarc srl della\ndisponibilità di acqua, corrente elettrica, idonei spazi di cantiere, ivi compreso l’uso di eventuali spazi comuni condominiali sopportandone i\nrelativi costi; d) gli eventuali costi per l’autorizzazione ad accedere con veicoli e mezzi nell’area in cui è situato l’immobile oggetto di\nintervento edilizio; e) ove la natura o l’entità delle opere appaltate richiedano ai sensi della normativa vigente la predisposizione del PSC, la\nredazione dello stesso."),

            ]
        )
    );

    contactPage.add(pw.NewPage());

    //------ Fourth Page
    contactPage.add(
        pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              _pointText("Il Committente si impegna a rendersi disponibile durante tutta la durata del cantiere per fornire riscontro tempestivo e prendere decisioni in\nmerito a qualsiasi problematica tecnica, organizzativa o progettuale che possa emergere nel corso dei lavori."),
              pw.SizedBox(height: 3),
              _pointText("In caso di problematiche rilevate, il Committente sarà informato tempestivamente dal Direttore dei Lavori o, in assenza di quest’ultimo, da"),
              pw.SizedBox(height: 3),
              _pointText("Newarc Srl in merito:"),
              pw.SizedBox(height: 3),
              _pointText(" • alla natura della problematica;"),
              pw.SizedBox(height: 3),
              _pointText(" • alle possibili soluzioni tecniche disponibili;"),
              pw.SizedBox(height: 3),
              _pointText(" • ai relativi costi, tempi e implicazioni."),
              pw.SizedBox(height: 3),
              _pointText("Il Committente potrà scegliere la soluzione che riterrà più idonea oppure delegare tale scelta al Direttore dei Lavori o a Newarc Srl.\nQualora il Committente non fornisca riscontro utile entro 2 (due) giorni lavorativi dalla comunicazione della problematica, Newarc Srl,\nnell’interesse del Committente e per garantire la continuità dei lavori, sarà autorizzata a procedere autonomamente adottando la soluzione\nche riterrà migliore sotto il profilo tecnico, economico e temporale."),

              pw.SizedBox(height: 20),

              _headerText("13. Divieti, esclusione di responsabilita"),
              pw.SizedBox(height: 5),
              _pointText("E’ fatto divieto al Committente di: a) far operare altre ditte nei locali occupati dalla Newarc srl per eseguire altre lavorazioni nel tempo\noccorrente alla realizzazione delle opere oggetto di contratto; b) impartire direttive esecutive o chiedere modifiche delle lavorazioni alle\nmaestranze presenti in cantiere ovvero corrispondere compensi a dette maestranze.\nNewarc srl non risponderà di opere realizzate in violazione del presente divieto e potrà esigere dal Committente il pagamento degli importi\nda questi già corrisposti in violazione del predetto divieto. "),


              pw.SizedBox(height: 36),
              pw.Center(child: _mainHeaderText("SOSPENSIONE DEI LAVORI, RISOLUZIONE")),
              pw.SizedBox(height: 20),
              _headerText("14. Sospensione lavori, risoluzione, penale per mancati o ritardati pagamenti"),
              pw.SizedBox(height: 5),
              _pointText("A) In presenza di Direzione Lavori nominata dal Committente, la mancata consegna dei SAL da parte del D.L. o l’incompletezza degli stessi\nconsente a Newarc srl di sospendere i lavori e, qualora tale mancata consegna o l’incompletezza di ciascun SAL si protragga per oltre 10\ngiorni dalla richiesta di Newarc srl, quest’ultimo avrà diritto di risolvere il presente contratto, di trattenere le somme già percepite e di\nchiedere il risarcimento dei danni subiti. B) Il mancato pagamento del 1° acconto nel termine di cui all’art. 7 consentirà a Newarc srl di\nposticipare unilateralmente le date di inizio e fine lavori. Qualora il mancato pagamento si protragga di oltre 10 giorni dalla data di\nsottoscrizione del contratto, Newarc srl avrà diritto di risolvere unilateralmente il presente contratto. C) Il mancato pagamento anche solo di\nuno degli altri acconti e/o del saldo entro le date di cui all’art. 7, darà diritto a Newarc srl di sospendere l’esecuzione delle opere fino\nall’avvenuto pagamento e, trascorsi 10 giorni dalla scadenza del termine, di risolvere unilateralmente il presente contratto. D) In caso di\nmancato rispetto dei termini di pagamento degli acconti e/o del saldo, il Committente sarà tenuto a pagare a Newarc srl la penale di euro\n50,00 per ogni giorno lavorativo di ritardo nel pagamento stesso. Tale penale decorrerà solo dal 7° giorno successivo alla scadenza del\nrelativo termine di pagamento. E) In tutti i casi di risoluzione del contratto previsti nel presente articolo, Newarc srl avrà diritto di ritenere\ntutte le somme già percepite, di ottenere il pagamento immediato sia di tutte le opere fino a quel momento eseguite, sia della penale, oltre\nal risarcimento dei maggiori danni eventualmente subiti"),

              pw.SizedBox(height: 36),
              pw.Center(child: _mainHeaderText("DEI MATERIALI")),
              pw.SizedBox(height: 20),
              _headerText("15. Fornitura materiali da parte del Committente"),
              pw.SizedBox(height: 5),
              _pointText("Il Committente che acquisti o fornisca autonomamente tutte o alcune forniture necessarie per l’inizio dei lavori e/o la loro realizzazione, si\nobbliga a fornire materiali idonei e a farli pervenire presso l’area di cantiere in tempo utile per la tempestiva esecuzione dei lavori a giudizio\ndel D.L., o di Newarc in caso di sua assenza, assicurando altresì il rispetto del termine di consegna delle opere e senza cagionare ritardi nella\nesecuzione ed ultimazione delle stesse. Nel caso in cui vi sia un ritardo nella fornitura dei materiali citati rispetto ai termini come sopra\nindividuati, il termine finale per la consegna dei lavori da parte di Newarc srl sarà prorogato per un periodo pari al ritardo maturato per la\nconsegna dei materiali presso il cantiere. Qualora i materiali forniti dal Committente, anche se tempestivamente consegnati, presentino vizi\ne/o difformità tali da renderne necessaria la sostituzione, sarà onere del Committente sostituirli ed il termine per la consegna ultima dei\nlavori sarà prorogato di un periodo pari a quello impiegato dal Committente per la consegna dei materiali così sostituiti."),

              pw.SizedBox(height: 20),
              _headerText("16. Fornitura materiali ordinati tramite Newarc o presso partner Newarc"),
              pw.SizedBox(height: 5),
              _pointText("Il Committente può acquistare i materiali di finitura dei partner Newarc tramite quest’ultima. In tal caso il Committente dovrà sottoscrivere\nl’apposito preventivo e provvedere al saldo integrale della fornitura entro 5 giorni dal ricevimento della fattura o pro forma di fattura e\ncomunque prima del termine di inizio dei lavori di cui all’art. 5. Il relativo ordine dei materiali verrà inoltrato all’azienda fornitrice solo previo\nintegrale pagamento anticipato del citato importo a Newarc. Il materiale può essere acquistato anche direttamente dal committente presso i\npartner Newarc ma in ogni caso dovrà esser fatto pervenire presso l’area di cantiere in tempo utile per la tempestiva esecuzione dei lavori a\ngiudizio del D.L. o di Newarc in caso di sua assenza, assicurando altresì il rispetto del termine di consegna delle opere e senza cagionare\nritardi nella esecuzione ed ultimazione delle stesse."),

            ]
        )
    );

    contactPage.add(pw.NewPage());

    //------ Fifth Page
    contactPage.add(
        pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              _headerText("17. Esonero di responsabilità di Newarc srl sia per ritardata o mancata consegna dei materiali di finitura, sia per vizi di tali materiali"),
              pw.SizedBox(height: 5),
              _pointText("Tutte le forniture acquistate tramite Newarc srl sono procurate tramite fornitori terzi e i termini di consegna indicati nei preventivi sono\nindicativi e possono subire variazioni. Newarc srl è, quindi, esonerato a) da qualunque responsabilità e/o danno derivante da eventuali ritardi\nnella consegna dei citati materiali e/o per eventuali vizi degli stessi.\nIn caso di eventuali ritardi nella consegna dei materiali o vizi degli stessi che ritardino l’esecuzione dei lavori appaltati, i termini per la consegna dei lavori saranno automaticamente prorogati di un tempo pari a\nquello necessario per ricevere o sostituire le forniture prescelte dal Committente e nulla sarà dovuto ad alcun titolo da Newarc srl"),

              pw.SizedBox(height: 20),

              _headerText("18. Risoluzione per mancata consegna dei materiali"),
              pw.SizedBox(height: 5),
              _pointText("Ove la consegna dei materiali di cui agli art.15 da parte del Committente si dovesse protrarre per un periodo superiore a 15 giorni rispetto al\ntermine previsto e/o pattuito con l’Newarc srl, sarà diritto di quest’ultimo risolvere il presente contratto per fatto e colpa del Committente.\nFermo il diritto alla penale di cui all’art. 15 lett. B), C), D), Newarc srl avrà altresì e comunque il diritto al pagamento delle somme maturate per\ni lavori effettuati fino a tale momento, salvo il maggior danno. In ogni caso Newarc srl non risponde dei danni conseguenti ai vizi dei materiali\nforniti dal Committente."),


              pw.SizedBox(height: 20),
              _headerText("19. Oneri del Committente per le forniture e limitazione di responsabilità"),
              pw.SizedBox(height: 5),
              _pointText("Nel caso in cui il Committente acquistasse il materiale per proprio conto di cui all’art. 15, egli è tenuto a verificare le eventuali difformità dei\nmateriali e la presenza di danni o vizi al momento del recapito in cantiere prima della loro utilizzazione. Tali difformità/danni/vizi dovranno\nessere comunicati e documentati immediatamente a Newarc srl per iscritto; in caso contrario Newarc srl non sarà responsabile per aver\ninstallato o utilizzato le forniture in oggetto e non sarà tenuto né alla loro sostituzione, né ad alcun risarcimento. E’ altresì esclusa ogni\nresponsabilità di Newarc srl qualora: a) successivamente all’impiego delle forniture di cui agli art. 15 e 16 il Committente abbia\nautonomamente manomesso o alterato i beni o abbia fatto intervenire soggetti terzi su vizi o malfunzionamenti; b) difetti o vizi dei materiali\nsiano riconoscibili con l’ordinaria diligenza o conosciuti ed accettati anche tacitamente dal Committente o da lui arrecati; c) i vizi siano\nderivanti dal logorio del normale uso o siano derivanti da manutenzioni, riparazioni, sostituzioni di singoli componenti effettuati da soggetti\nnon autorizzati."),


              pw.SizedBox(height: 36),
              pw.Center(child: _mainHeaderText("MODIFICHE AL CAPITOLATO")),

              pw.SizedBox(height: 20),
              _headerText("20. Modifiche volontarie al capitolato"),
              pw.SizedBox(height: 5),
              _pointText("Newarc srl non potrà effettuare alcuna variazione delle opere da eseguire senza il consenso espresso del Committente. Quest’ultimo potrà\nchiedere modifiche quantitative e qualitative delle opere comunicandole tempestivamente a Newarc srl. Quest’ultimo, a suo insindacabile\ngiudizio: a) verificherà preliminarmente la fattibilità delle modifiche richieste; b) qualora le ritenga fattibili, comunicherà al Committente le\nvariazioni di prezzo e di tempistiche che tali modifiche potranno comportare; c) In qualunque caso le modifiche non potranno comportare\nuna variazione negativa del prezzo oltre il 10% del valore espresso nella sezione apposita che riguarda tale modifica. Tali variazioni di prezzo\ne di tempistiche dovranno essere accettate dal Committente entro 3 giorni dalla loro ricezione. Il Committente corrisponderà al momento\ndell’accettazione in unica soluzione o in caso di nuovo preventivo specifico per la nuova opera nei termini espressi all’interno di quest’ultimo,\nil prezzo pattuito per le variazioni richieste. Le modifiche volontarie In difetto di tali accettazione o pagamento le variazioni richieste non\nverranno apportate."),


              pw.SizedBox(height: 20),
              _headerText("21. Modifiche necessarie al capitolato"),
              pw.SizedBox(height: 5),
              _pointText("Qualora, nel corso dell’esecuzione dell’opera, a causa delle condizioni dei luoghi non conosciute o conoscibili durante i sopralluoghi\npreliminari, sorgesse la necessità di apportare modifiche tecniche alle opere concordate al fine di garantire la diligente realizzazione delle\nstesse, Newarc srl ne darà comunicazione scritta o telematica via app o email al Committente al fine di concordare con quest’ultimo la\nsoluzione tecnica da adottare.\nIndividuata di comune accordo la soluzione da adottare, Newarc srl entro 3 giorni lavorativi comunicherà al Committente le variazioni di\nprezzo e di tempistiche che tali modifiche potranno comportare e che dovranno essere accettate per iscritto dal Committente. Il pagamento\ndi tali opere ulteriori dovrà avvenire contestualmente alla loro approvazione.\nIn ogni caso il termine di consegna dei lavori pattuito sarà prorogato del tempo occorso per individuare congiuntamente la migliore\nsoluzione tecnica da adottare, nonché dei tempi necessari per la realizzazione delle ulteriori nuove opere accettate dal Committente. In\ncaso di mancato accordo tra le parti sulla soluzione più idonea da adottare e/o sul prezzo, entrambe potranno recedere dal contratto,\nconservando Newarc srl il diritto alla corresponsione del compenso per le opere già eseguite."),
            ]
        )
    );

    contactPage.add(pw.NewPage());

    //------ Seventh Page
    contactPage.add(
        pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Center(child: _mainHeaderText("ALTRI CASI DI RISOLUZIONE E PENALE")),

              pw.SizedBox(height: 20),
              _headerText("22. Risoluzione del Committente"),
              pw.SizedBox(height: 5),
              _pointText("In caso di grave inadempimento di Newarc srl, il Committente potrà chiedere la risoluzione del contratto, dandone comunicazione a mezzo\nraccomandata a.r. o Pec a Newarc srl al quale spetterà comunque il pagamento dei lavori già eseguiti e dei materiali d’opera presenti in\ncantiere, senza alcun onere aggiuntivo, fermo restando il suo obbligo di risarcire al Committente tutti gli eventuali danni conseguenti al\nproprio inadempimento."),

              pw.SizedBox(height: 20),

              _headerText("23. Risoluzione di Newarc Srl"),
              pw.SizedBox(height: 5),
              _pointText("Oltre nei casi già previsti, è facoltà di Newarc srl di risolvere il presente contratto in caso di grave inadempimento del Committente nei\nseguenti casi: a) mancata fornitura, salvo diversa pattuizione, dei permessi amministrativi; b) qualora il Committente chieda la sospensione\ndei lavori per esigenze personali che possano ostacolare l’ordinato svolgimento delle opere o che aggravi economicamente i costi di"),
              pw.SizedBox(height: 3),
              pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _pointText("Newarc srl; c) qualora il Committente, in corso d’opera, richieda di eliminare un quantitativo di opere superiore al  "),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                              contract.acceptedRemovalPercentage != null ? contract.acceptedRemovalPercentage.toString() :  "",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                          pw.Text(
                              "..............",
                              tightBounds: true,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 8,
                                  color: PdfColors.black
                              )
                          ),
                        ]
                    ),
                    _pointText("  % dell’importo del"),
                  ]
              ),
              pw.SizedBox(height: 3),
              _pointText("contratto."),
              pw.SizedBox(height: 20),
              _headerText("24. Penale per illegittima cessazione anticipata del contratto da parte del Committente"),
              pw.SizedBox(height: 5),
              _pointText("In caso di cessazione anticipata del presente contratto per fatto o colpa o volontà del Committente non dovuta da fatto o colpa di Newarc srl,\nquest’ultimo avrà diritto al pagamento delle opere fino a quel momento realizzate oltre al pagamento di una penale pari al 10%\ndell’importo previsto per le opere già commissionate e non realizzate."),

              pw.SizedBox(height: 20),
              _headerText("25. Penale per ritardata consegna dei lavori"),
              pw.SizedBox(height: 5),
              _pointText("In caso di mancato rispetto dei termini di consegna dei lavori di cui all’art. 5 dipendente da esclusiva responsabilità di Newarc srl, questi si\nobbliga a corrispondere al Committente, a tacitazione di ogni danno dipendente dal ritardo, la penale di € 50 per ogni giorno lavorativo\nsuccessivo al settimo giorno dalla data prevista per la consegna dei lavori."),

              pw.SizedBox(height: 36),
              pw.Center(child: _mainHeaderText("CLAUSOLE FINALI")),

              pw.SizedBox(height: 20),
              _headerText("26. Foro competente"),
              pw.SizedBox(height: 5),
              _pointText("Per qualsiasi controversia dovesse insorgere tra le parti in relazione alla interpretazione, esecuzione, risoluzione del presente contratto sarà\nesclusivamente competente il giudice del luogo di residenza del consumatore o del luogo in cui questi abbia eletto domicilio."),

              pw.SizedBox(height: 20),
              _headerText("27. Trattamento dei dati personali"),
              pw.SizedBox(height: 5),
              _pointText("Il Committente autorizza Newarc srl al trattamento dei propri dati personali anche con strumenti informatici in relazione agli adempimenti\nconnessi al presente contratto, dichiarando di essere stato adeguatamente informato circa le modalità e le finalità del trattamento, come da\ninformativa allegata e sottoscritta dal Committente."),

              pw.SizedBox(height: 130),

              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Row(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          _pointText("Torino, lì  "),
                          pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                pw.Text(
                                  '{{DATE_HERE_1}}',
                                  style: pw.TextStyle(
                                    fontSize: 1, // Invisible
                                    color: PdfColor.fromHex("#F3F3F3"), // Hidden in white background
                                  ),
                                ),
                                pw.Text(
                                    "....................................",
                                    tightBounds: true,
                                    style: pw.TextStyle(
                                        font: ralewayMedium,
                                        fontSize: 10,
                                        color: PdfColors.black
                                    )
                                ),
                              ]
                          ),
                        ]
                    ),
                    pw.SizedBox(height: 24),
                    pw.SizedBox(
                      width: double.infinity,
                      child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Row(
                                crossAxisAlignment: pw.CrossAxisAlignment.center,
                                children: [
                                  _pointText("Newarc Srl"),
                                  pw.SizedBox(width: 24),
                                  pw.Container(
                                      height: 100,
                                      width: 148,
                                      decoration: pw.BoxDecoration(
                                          // color: PdfColor.fromHex("#F3F3F3"),
                                          borderRadius: pw.BorderRadius.circular(5),
                                          image: pw.DecorationImage(image: signAndStamp,fit: pw.BoxFit.fitWidth)
                                      ),
                                  )
                                ]
                            ),
                            pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              children: [
                                _pointText("Il Committente"),
                                pw.SizedBox(width: 24),
                                pw.Container(
                                  height: 42,
                                  width: 148,
                                  decoration: pw.BoxDecoration(
                                    color: PdfColor.fromHex("#F3F3F3"),
                                    borderRadius: pw.BorderRadius.circular(5),
                                  ),
                                  alignment: pw.Alignment.bottomLeft,
                                  child: pw.Padding(
                                    padding: const pw.EdgeInsets.all(4),
                                    child: pw.Text(
                                      '{{SIGN_HERE_1}}',
                                      style: pw.TextStyle(
                                        fontSize: 1, // Invisible
                                        color: PdfColor.fromHex("#F3F3F3"), // Hidden in white background
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ]
                      )
                    )
                  ]
              ),

            ]
        )
    );

    contactPage.add(pw.NewPage());

    //------ Sixth Page
    contactPage.add(
        pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Center(child: _mainHeaderText("APPROVAZIONE SPECIFICA")),
              pw.SizedBox(height: 36),
              _pointText("Ai sensi e per gli effetti di cui agli artt. 1341, 1342 c.c. si approvano specificamente, dopo averne presa attenta lettura ed averle comprese, le\nseguenti clausole: 4 Inizio lavori lett A) e B); 5 Consegna dei lavori; 6 Pagamenti e SAL; 7 Verifica finale e verbale di consegna dei lavori lett.\nB); 8 Certificazione impianti; 9 Garanzia e interventi in garanzia; 10 Collaborazione delle parti; 11 Obblighi di Newarc ed esonero di\nresponsabilità; 12 Oneri del Committente; 13 Divieti, esclusione di responsabilità; 14 Sospensioni dei lavori, risoluzione, penale per mancati o\nritardati pagamenti lett. A, B), C), D), E); 15 Fornitura materiali da parte del Committente; proroghe consegna; penali lett. B), C), D); 16 Fornitura\nmateriali ordinati tramite Newarc o presso partner Newarc 17 Esonero di responsabilità di Newarc sia per ritardata o mancata consegna dei\nmateriali di finitura, sia per vizi di tali materiali; 18 Risoluzione per mancata consegna dei materiali- limitazione di responsabilità; 19 Oneri del\nCommittente per le forniture e limitazione di responsabilità; 20 Modifiche volontarie al capitolato; 21 Modifiche necessarie al capitolato; 22\nRisoluzione del Committente; 23 Risoluzione di Newarc srl; 24 Penale per illegittima cessazione anticipata del contratto da parte del\nCommittente; 25 Penale per ritardata consegna dei lavori 26 Foro competente di Torino; 27 Trattamento dei dati personali."),
              pw.SizedBox(height: 50),
              pw.SizedBox(
                  width: double.infinity,
                  child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Row(
                            crossAxisAlignment: pw.CrossAxisAlignment.end,
                            children: [
                              _pointText("Torino, lì  "),
                              pw.Column(
                                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      '{{DATE_HERE_1}}',
                                      style: pw.TextStyle(
                                        fontSize: 1, // Invisible
                                        color: PdfColor.fromHex("#F3F3F3"), // Hidden in white background
                                      ),
                                    ),
                                    pw.Text(
                                        "....................................",
                                        tightBounds: true,
                                        style: pw.TextStyle(
                                            font: ralewayMedium,
                                            fontSize: 10,
                                            color: PdfColors.black
                                        )
                                    ),
                                  ]
                              ),
                            ]
                        ),
                        pw.Row(
                          crossAxisAlignment: pw.CrossAxisAlignment.center,
                          children: [
                            _pointText("Il Committente"),
                            pw.SizedBox(width: 24),
                            pw.Container(
                              height: 42,
                              width: 148,
                              decoration: pw.BoxDecoration(
                                color: PdfColor.fromHex("#F3F3F3"),
                                borderRadius: pw.BorderRadius.circular(5),
                              ),
                              alignment: pw.Alignment.bottomLeft,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(4),
                                child: pw.Text(
                                  '{{SIGN_HERE_2}}',
                                  style: pw.TextStyle(
                                    fontSize: 1, // Invisible
                                    color: PdfColor.fromHex("#F3F3F3"), // Hidden in white background
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      ]
                  )
              )

            ]
        )
    );




    pdf.addPage(
        pw.MultiPage(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat.a4,
          orientation: pw.PageOrientation.portrait,
          margin: const pw.EdgeInsets.only(left: 30,right: 30,top: 26,bottom: 20),
          build: (pw.Context context) => contactPage,
          footer: (pw.Context context) => pageFooter(context.pageNumber.toString()),
        )
    );

    // Save PDF to bytes
    final pdfBytes = await pdf.save();
    if(isPDFDownload){
      // Create a Blob from PDF bytes
      final blob = html.Blob([pdfBytes], 'application/pdf');
      // Create a link element
      final url = html.Url.createObjectUrlFromBlob(blob);
      html.AnchorElement(href: url)
        ..setAttribute('download', "Contratto_Ristrutturazione_${quotation.code}_V${quotation.version}_R${quotation.revision}" + '.pdf')
        ..click();

      // Clean up
      html.Url.revokeObjectUrl(url);
    }else{
      // final bytes = base64Decode(pdfBytes.toString());
      // log("bytes ===> ${bytes}");
      // log("bytes runtimeType ===> ${bytes.runtimeType}");
      return pdfBytes;
    }


  }catch(e,s){
    log("Error while downloading contract PDF ----> ${e.toString()}");
    log("Stacktrace while downloading contract PDF ----> ${s.toString()}");
  }
  return null;
}


