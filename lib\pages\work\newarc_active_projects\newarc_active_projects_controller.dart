import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';


class NewarcActiveProjectsViewController extends GetxController {
  RxBool loadingProperties = false.obs;
  RxBool loadingContacts = false.obs;

  List<NewarcProject> projects = [];
  List<Map> projectEconomicData = [];
  List<ProjectEconomic> projectEconomic = [];

  List<RenovationQuotation> renovationContact = [];
  List<Map> contacts = [];
  String currentlyShowing = '';

  int totalRecords = 0;
  final recordsPerPage = 20;

  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;

  List<DocumentSnapshot> documentList = [];

  List<dynamic> cacheFirestore = [];

  List<String> formMessages = [];
  TextEditingController contProjectName = new TextEditingController();
  TextEditingController contProjectType = new TextEditingController();
  TextEditingController contProjectEconomic = new TextEditingController();
  TextEditingController contCity = new TextEditingController();
  TextEditingController contZone = new TextEditingController();

  final TextEditingController contSearchProjectTypeController = new TextEditingController();
  String contSearchProjectType = '';
  final TextEditingController contSearchText = new TextEditingController();
  final TextEditingController searchTextController = new TextEditingController();


  final TextEditingController contRenovationContact = new TextEditingController();

  bool titleEnabled = true;

  String query = "";
  List<Map> filters = [];
  String currentMenu = 'progetti-in-corso';
}
