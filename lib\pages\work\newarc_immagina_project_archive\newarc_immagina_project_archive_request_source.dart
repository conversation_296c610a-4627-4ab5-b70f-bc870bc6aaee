
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_svg/svg.dart';

import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/custom_textformfield.dart';
import '../../../widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class NewarcImmaginaProjectArchiveRequestSource extends DataTableSource {
  NewarcImmaginaProjectArchiveRequestSource({
    this.onAddressTap,
    required this.projects,
    required this.context,
  });

  List<ImmaginaProject> projects = [];

  final BuildContext context;

  Function(ImmaginaProject)? onAddressTap;

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final project = projects[index];
      var address = "${project.streetName ?? 'noStreetName'}, ${project.streetNumber ?? 'noStreetNum'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      if (project.addressInfo != null) {
        address = "${project.addressInfo!.streetName ?? 'noStreetName'} ${project.addressInfo!.streetNumber ?? 'noStreetNum'}, ${project.addressInfo!.city ?? 'noCity'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      }
      bool isProfessionals = project.isForProfessionals ?? false;
      bool isSmart = project.isSmart;

      return DataRow(
        cells: [
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (isProfessionals || isSmart)
                  Positioned(
                    top: -24,
                    child: Row(
                      children: [
                        TagWidget(
                          text:  isProfessionals ? "Professionals" : "Smart",
                          statusColor: isProfessionals ? Colors.black : null,
                          changingColors : isSmart ? [
                            Color(0xff5abdb5),
                            Color(0xff499b79),
                            Color(0xff145935),
                          ] : null,
                        ),
                      ],
                    ),
                  ),
                NarFormLabelWidget(
                  label: project.projectId,
                  fontSize: 12,
                  fontWeight: '600',
                  textColor: AppColor.black,
                ),
              ],
            ),
          ),

          // ///Codice
          // DataCell(
          //   NarFormLabelWidget(
          //     label: project.projectId,
          //     fontSize: 12,
          //     fontWeight: '600',
          //     textColor: AppColor.black,
          //   ),
          // ),

          ///Indirizzo
          DataCell(
            NarLinkWidget(
              text: address,
              textColor: Colors.black,
              fontWeight: '700',
              fontSize: 12,
              overflow: TextOverflow.ellipsis,
              onClick: () {
                onAddressTap!(project);
              },
            ),
          ),

          ///Agenzia
          DataCell(
            FutureBuilder<DocumentSnapshot>(
              future: 
              project.professionalId == null
                ? FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(project.agencyId).get()
                : FirebaseFirestore.instance.collection(appConfig.COLLECT_PROFESSIONALS).doc(project.professionalId).get(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return NarFormLabelWidget(
                    label: 'Loading...',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  );
                }
                if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                  return NarFormLabelWidget(
                    label: '',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  );
                }
                Map ownerData = snapshot.data!.data()! as Map<String, dynamic>;
                String name = project.professionalId == null ? ownerData["name"] : ownerData["companyName"];
                return NarLinkWidget(
                  onClick: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return Center(
                          child: BaseNewarcPopup(
                            noButton: true,
                            title: project.professionalId == null ? "Agenzia: ${ownerData["name"]}" : "Professional: ${ownerData["companyName"]}",
                            column: Container(
                              width: 400,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(height: 20),
                                  CustomTextFormField(
                                    textAlign: TextAlign.center,
                                    isHaveBorder: true,
                                    isCenterLabel: false,
                                    flex: 0,
                                    suffixIcon: null,
                                    readOnly: true,
                                    labelColor: AppColor.greyColor,
                                    fillColor: Color(0xffF2F2F2),
                                    label: "Telefono",
                                    controller: TextEditingController(
                                      text: ownerData["phone"],
                                    ),
                                  ),
                                  SizedBox(height: 15),
                                  CustomTextFormField(
                                    textAlign: TextAlign.center,
                                    isHaveBorder: true,
                                    isCenterLabel: false,
                                    flex: 0,
                                    suffixIcon: null,
                                    readOnly: true,
                                    labelColor: AppColor.greyColor,
                                    fillColor: Color(0xffF2F2F2),
                                    label: "E-mail",
                                    controller: TextEditingController(
                                      text: ownerData["email"],
                                    ),
                                  ),
                                  SizedBox(height: 15),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                  text: name,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 12,
                  fontWeight: '700',
                  textColor: AppColor.black,
                );
              },
            ),
          ),

          /// Acquirente
          DataCell(
            IconButtonWidget(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return Center(
                      child: BaseNewarcPopup(
                        noButton: true,
                        title: "Dati acquirente",
                        column: Container(
                          width: 400,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(height: 20),
                              CustomTextFormField(
                                textAlign: TextAlign.center,
                                isHaveBorder: true,
                                isCenterLabel: false,
                                flex: 0,
                                suffixIcon: null,
                                readOnly: true,
                                labelColor: AppColor.greyColor,
                                fillColor: Color(0xffF2F2F2),
                                label: "Nome e cognome acquirente",
                                controller: TextEditingController(
                                  text: project.acquirerName != null
                                      ? project.acquirerName
                                      : "",
                                ),
                              ),
                              SizedBox(height: 15),
                              CustomTextFormField(
                                textAlign: TextAlign.center,
                                isHaveBorder: true,
                                isCenterLabel: false,
                                flex: 0,
                                suffixIcon: null,
                                readOnly: true,
                                labelColor: AppColor.greyColor,
                                fillColor: Color(0xffF2F2F2),
                                label: "Telefono acquirente",
                                controller: TextEditingController(
                                  text: project.acquirerPhoneNumber != null
                                      ? project.acquirerPhoneNumber
                                      : "",
                                ),
                              ),
                              SizedBox(height: 15),
                              CustomTextFormField(
                                textAlign: TextAlign.center,
                                isHaveBorder: true,
                                isCenterLabel: false,
                                flex: 0,
                                suffixIcon: null,
                                readOnly: true,
                                labelColor: AppColor.greyColor,
                                fillColor: Color(0xffF2F2F2),
                                label: "E-mail acquirente",
                                controller: TextEditingController(
                                  text: project.acquirerEmailId != null
                                      ? project.acquirerEmailId
                                      : "",
                                ),
                              ),
                              SizedBox(height: 15),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              isSvgIcon: true,
              icon: 'assets/icons/account.svg',
              iconColor: AppColor.greyColor,
            ),
          ),

          /// Vendita
          DataCell(
              StatusWidget(
                status: project.isHouseSold ? CommonUtils.venduto : CommonUtils.nonVenduto,
                statusColor: CommonUtils.getColor(project.isHouseSold ? CommonUtils.venduto : CommonUtils.nonVenduto),
              )

          ),

          /// Vendita
          DataCell(
            NarFormLabelWidget(
              label: project.sellingPrice != null
                  ? '€ ' + CommonUtils().formatStringToDecimal(input: project.sellingPrice.toString())
                  : "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
        ],
      );
    }

    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
