import 'package:newarc_platform/utils/various.dart';

class NewarcUser {
  String? id;
  String? firstName;
  String? lastName;
  String? email;
  String? type;
  String? role;
  String? profilePicture;
  bool? isActive;
  bool? isArchived;
  bool? isHiredInternally;
  Map? menuAccess;
  Map? projectTabAccess;
  bool? isFilterPerAccountEnabled;
  String? phoneCode;
  String? phone;

  NewarcUser(Map<String, dynamic> userMap) {
    this.id = userMap['id'];
    this.firstName = userMap['name'];
    this.lastName = userMap['surname'];
    this.email = userMap['email'];
    this.type = userMap['type'];
    this.role = userMap['role'].toString().toTitleCase().replaceAll(' ', '_');
    this.profilePicture = userMap['profilePicture'];
    this.isActive = userMap['isActive'] != null ? userMap['isActive'] : true;
    this.isArchived = userMap['isArchived'] != null ? userMap['isArchived'] : false;
    this.isHiredInternally = userMap['isHiredInternally'];
    this.menuAccess = userMap['menuAccess'];
    this.projectTabAccess = userMap['projectTabAccess'];
    this.isFilterPerAccountEnabled = userMap['isFilterPerAccountEnabled']??true;
    this.phoneCode = userMap['phoneCode']??'';
    this.phone = userMap['phone']??'';
}
  NewarcUser.empty() {
    this.id = '';
    this.firstName = '';
    this.lastName = '';
    this.email = '';
    this.type = '';
    this.role = '';
    this.isActive = true;
    this.profilePicture = '';
    this.isArchived = false;
    this.isHiredInternally = null;
    this.menuAccess = null;
    this.isFilterPerAccountEnabled = true;
    this.projectTabAccess = null;
    this.phone = '';
    this.phoneCode = '';
  }

  NewarcUser.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.firstName = data['name'];
    this.lastName = data['surname'];
    this.email = data['email'];
    this.type = data['type'];
    this.role = data['role']??'';
    this.profilePicture = data['profilePicture'];
    this.isActive = data['isActive'] != null ? data['isActive'] : true;
    this.isArchived = data['isArchived'] != null ? data['isArchived'] : false;
    this.isHiredInternally = data['isHiredInternally'];
    this.menuAccess = data['menuAccess']??null;
    this.isFilterPerAccountEnabled = data['isFilterPerAccountEnabled']??true;
    this.projectTabAccess = data['projectTabAccess']??getDefaultProjectTabByRole(data['role']);
    this.phone = data['phone']??'';
    this.phoneCode = data['phoneCode']??'';
  }

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'name': this.firstName,
      'surname': this.lastName,
      'email': this.email,
      'type': this.type!.toLowerCase().replaceAll(' ', '_'),
      'role': this.role!.toLowerCase().replaceAll(' ', '_'),
      'profilePicture': this.profilePicture,
      'isActive': this.isActive,
      'isArchived': this.isArchived,
      'isHiredInternally': this.isHiredInternally,
      'menuAccess': this.menuAccess,
      'isFilterPerAccountEnabled': this.isFilterPerAccountEnabled??true,
      'projectTabAccess': this.projectTabAccess??null,
      'phone': this.phone??'',
      'phoneCode': this.phoneCode??'',
    };
  }
}
