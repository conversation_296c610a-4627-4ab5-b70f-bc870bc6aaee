import 'dart:math';
import 'dart:typed_data';
import 'dart:html' as html;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/dropdown_search.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/checkbox.dart';
import 'package:newarc_platform/widget/UI/si_no_button.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as p;
import '../../classes/immaginaProject.dart';
import '../../classes/reportAcquirente.dart';
import '../../functions/various.dart';
import '../../utils/heicToJpegConverter.dart' as heicToJpeg;

class BuyerReportDataInsertionPopup extends StatefulWidget {
  final String procedureStep;
  final ReportAcquirente? project;
  final List<String> formErrorMessage;
  final AgencyUser agencyUser;
  final  Function(String id) onClose;
  final bool isFromEdit;

  BuyerReportDataInsertionPopup({
    required this.procedureStep,
    this.project,
    this.formErrorMessage = const [],
    required this.agencyUser,
    required this.onClose(String id),
    required this.isFromEdit,
    Key? key,
  }) : super(key: key) {
    if(isFromEdit){
      if (project == null) {
        throw ArgumentError(
          'If project is null',
        );
      }
    }
  }

  @override
  State<BuyerReportDataInsertionPopup> createState() => _BuyerReportDataInsertionPopupState();
}

class _BuyerReportDataInsertionPopupState extends State<BuyerReportDataInsertionPopup> {
  TextStyle sectionTitleStyle = TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool clicked = false;
  List<String> formErrorMessage = [];
  ImagePicker picker = ImagePicker();

  List<ImmaginaComparabili>? allComparabile;

  List viewsOrder = const [
    'initial',
    'localizzazione',
    'info-generali',
    'caratteristiche',
    'info-sullo-stabile',
    'riscaldamento-energetica',
    'descrizione',
    'descrizione-del-quartiere',
    'prospetto-costi',
    'planimetria',
    'fotografie-interne',
    'fotografie-esterne',
    // 'virtual-tour',
    'stima-ristrutturazione',
    'final-data-entry'
  ];

  Map<String, String> _errorMessage = {
    'localizzazione': 'Inserisci tutti i campi richiesti prima di procedere',
    'info-generali': 'Inserisci tutti i campi richiesti prima di procedere',
    'info-sullo-stabile': 'Inserisci tutti i campi richiesti prima di procedere',
    'riscaldamento-energetica': 'Inserisci tutti i campi richiesti prima di procedere',
    'descrizione': 'Inserisci tutti i campi richiesti prima di procedere',
    'descrizione-del-quartiere': 'Inserisci tutti i campi richiesti prima di procedere',
    'caratteristiche': 'Inserisci tutti i campi richiesti prima di procedere',
    'prospetto-costi': 'Inserisci tutti i campi richiesti prima di procedere',
    'planimetria': 'Inserisci tutti i campi richiesti prima di procedere',
    'fotografie-interne': 'Inserisci tutti i campi richiesti prima di procedere',
    'fotografie-esterne': 'Inserisci tutti i campi richiesti prima di procedere',
    'virtual-tour': 'Inserisci tutti i campi richiesti prima di procedere',
    'stima-ristrutturazione': 'Inserisci tutti i campi richiesti prima di procedere',
  };

  List<String> qualityAreaList = [
    'Zona normale',
    'Zona ottima',
    'Zona di pregio'
  ];

  List<String> allowedPlanimetryExtensions = ['png', 'jpg', 'jpeg'];
  List<String> allowedPicturesExtensions = ['jpg', 'jpeg','heic'];

  Map<String, List<String>> cityMarketZoneMap = appConst.cityZones;

  // Images loaders
  late List<XFile> planimetryImages = [];
  late List<Map<String, dynamic>> picturesImages = [];

  // Popup main state variables
  String? selectedView;
  ReportAcquirente? selectedProject;

  // Pagamento state variable
  bool _isFrozen = false;

  // Localizzazione
  TextEditingController filterMarketZone = TextEditingController();

  // Info generali
  TextEditingController filterGSF = TextEditingController();
  TextEditingController filterRooms = TextEditingController();
  TextEditingController filterBathrooms = TextEditingController();
  TextEditingController filterUnitFloor = TextEditingController();
  TextEditingController filterPropertyType = TextEditingController();
  TextEditingController filterStato = TextEditingController();
  TextEditingController filterObjective = TextEditingController();
  TextEditingController filterStateOfFixtures = TextEditingController();

  // Prospetto costi
  TextEditingController filterListingPrice = TextEditingController();
  TextEditingController filterAgencyCost = TextEditingController();
  TextEditingController filterNotaryCost = TextEditingController();
  TextEditingController filterRegistrationTax = TextEditingController();

  // Info sullo stabile
  TextEditingController filterTipologiaStabile = TextEditingController();
  TextEditingController filterCategory = TextEditingController();
  TextEditingController filterTotalPlans = TextEditingController();
  TextEditingController filterCondominiumExpenses = TextEditingController();
  TextEditingController filterHeating = TextEditingController();
  TextEditingController filterStableConditions = TextEditingController();
  TextEditingController filterHeatingCosts = TextEditingController();


  // Descrizione
  TextEditingController filterDescription = TextEditingController();
  TextEditingController filterDescriptionNeighborhood = TextEditingController();

  // Virtual Tour
  TextEditingController filterVirtualTour = TextEditingController();

  // Stima ristrutturazione
  TextEditingController filterNumberOfInternalDoors = TextEditingController();
  TextEditingController filterWalkableSquareMeters = TextEditingController();
  TextEditingController filterMqOfFixtures = TextEditingController();
  TextEditingController filterQualityOfTheArea = TextEditingController();


  // Caratteristiche
  Map<String, bool> characteristicsMap = Map.from(appConst.houseFeatures);


  TextEditingController filterEnergyClass = TextEditingController();
  TextEditingController filterConstructionYear = TextEditingController();
  // Indicazioni Speciali
  TextEditingController filterSpecialHints = TextEditingController();
  TextEditingController filterPropertyUpForSaleAnswer = TextEditingController();

  void initializeControllers() {
    // Localizzazione
    filterMarketZone.text = selectedProject!.marketZone ?? "";

    // Info generali
    filterGSF.text = selectedProject!.grossSquareFootage != null ? selectedProject!.grossSquareFootage.toString() : "";
    filterRooms.text = selectedProject!.rooms != null ? selectedProject!.rooms.toString() : "";
    if (filterRooms.text == "5") {
      filterRooms.text = "5+";
    }
    filterBathrooms.text = selectedProject!.numberOfBathrooms != null ? selectedProject!.numberOfBathrooms.toString() : "";
    if (filterBathrooms.text == "3") {
      filterBathrooms.text = "3+";
    }
    filterUnitFloor.text = selectedProject!.unitFloor ?? "";
    filterCategory.text = selectedProject!.category ?? "";
    filterListingPrice.text = selectedProject!.listingPrice != null ?  localCurrencyFormatMain.format(selectedProject!.listingPrice).toString() : "";
    filterPropertyType.text = selectedProject!.propertyType != null ? selectedProject!.propertyType.toString() : "";
    // Descrizione
    filterDescription.text = selectedProject!.description ?? "";
    filterDescriptionNeighborhood.text = selectedProject!.descriptionNeighborhood ?? "";
    // Caratteristiche
    characteristicsMap['Ascensore'] = selectedProject!.elevator ?? false;
    characteristicsMap['Cantina'] = selectedProject!.hasCantina ?? false;
    characteristicsMap['Terrazzo'] = selectedProject!.terrace ?? false;
    characteristicsMap['Portineria'] = selectedProject!.hasConcierge ?? false;
    characteristicsMap['Infissi ad alta efficienza'] = selectedProject!.highEfficiencyFrames ?? false;
    characteristicsMap['Doppia esposizione'] = selectedProject!.doubleEsposition ?? false;
    characteristicsMap['Tripla esposizione'] = selectedProject!.tripleEsposition ?? false;
    characteristicsMap['Quadrupla esposizione'] = selectedProject!.quadrupleEsposition ?? false;
    characteristicsMap['Risc. centralizzato'] = selectedProject!.centralizedHeating ?? false;
    characteristicsMap['Risc. autonomo'] = selectedProject!.autonomousHeating ?? false;
    characteristicsMap['Giardino privato'] = selectedProject!.privateGarden ?? false;
    characteristicsMap['Giardino condominiale'] = selectedProject!.sharedGarden ?? false;
    characteristicsMap['Stabile signorile'] = selectedProject!.nobleBuilding ?? false;
    characteristicsMap['Stabile videosorvegliato'] = selectedProject!.surveiledBuilding ?? false;
    characteristicsMap['Fibra ottica'] = selectedProject!.fiber ?? false;
    characteristicsMap['Pred. condizionatore'] = selectedProject!.airConditioning ?? false;
    characteristicsMap['Porta blindata'] = selectedProject!.securityDoor ?? false;
    characteristicsMap['Impianto TV'] = selectedProject!.tvStation ?? false;
    characteristicsMap['Pred. antifurto'] = selectedProject!.alarm ?? false;
    characteristicsMap['Tapparelle motorizzate'] = selectedProject!.motorizedSunblind ?? false;
    characteristicsMap['Tapparelle domotizzate'] = selectedProject!.domotizedSunblind ?? false;
    characteristicsMap['Luci domotizzate'] = selectedProject!.domotizedLights ?? false;
    characteristicsMap['Piano alto'] = selectedProject!.highFloor ?? false;
    characteristicsMap['Vicinanza Metro'] = selectedProject!.metroVicinity ?? false;
    characteristicsMap['Ampi balconi'] = selectedProject!.bigBalconies ?? false;
    characteristicsMap['Grande zona living'] = selectedProject!.bigLiving ?? false;
    characteristicsMap['Doppi servizi'] = selectedProject!.doubleBathroom ?? false;
    characteristicsMap['Piscina'] = selectedProject!.swimmingPool ?? false;
    characteristicsMap['Box o garage'] = selectedProject!.hasGarage ?? false;
    characteristicsMap['Cabina armadio'] = selectedProject!.walkInCloset ?? false;
    characteristicsMap['Fotovoltaico'] = selectedProject!.solarPanel ?? false;

    filterEnergyClass.text = selectedProject!.actualEnergyClass ?? "";
    filterObjective.text = selectedProject!.objective ?? "";
    filterConstructionYear.text = selectedProject!.constructionYear != null ? selectedProject!.constructionYear.toString() : "";
    // Indicazioni Speciali
    filterSpecialHints.text = selectedProject!.specialHints ?? "";
    filterPropertyUpForSaleAnswer.text = selectedProject!.propertyUpForSaleAnswer ?? "";

    filterStato.text = selectedProject!.buyerReportStatus ?? "";
    filterStateOfFixtures.text = selectedProject!.stateOfFixtures ?? "";

    filterVirtualTour.text = selectedProject!.reportVirtualTour ?? "";

    // Prospetto costi
    filterAgencyCost.text = selectedProject!.landRegistryIncome != null ? localCurrencyFormatMain.format(selectedProject!.landRegistryIncome).toString() : "";
    filterNotaryCost.text = selectedProject!.notaryCost != null ?   localCurrencyFormatMain.format(selectedProject!.notaryCost).toString() : "";
    filterRegistrationTax.text = selectedProject!.registrationTax != null ? localCurrencyFormatMain.format(selectedProject!.registrationTax).toString() : "";

    // Info sullo stabile
    filterTipologiaStabile.text = selectedProject!.tipologiaStabile ?? "";
    filterTotalPlans.text = selectedProject!.totalPlans ?? "";
    filterCondominiumExpenses.text = selectedProject!.condominiumExpenses != null ? localCurrencyFormatMain.format(selectedProject!.condominiumExpenses).toString() : "";
    filterHeating.text = selectedProject!.heating ?? "";
    filterHeatingCosts.text = selectedProject!.heatingCosts != null ?  localCurrencyFormatMain.format(selectedProject!.heatingCosts).toString() : "";
    filterStableConditions.text = selectedProject!.stableCondition ?? "";

    // Stima ristrutturazione
    // filterNumberOfInternalDoors.text = selectedProject!.numberOfInternalDoors != null ? selectedProject!.numberOfInternalDoors.toString() : "";
    // filterWalkableSquareMeters.text = selectedProject!.walkableSquareMeters != null ? selectedProject!.walkableSquareMeters.toString() : "";
    // filterMqOfFixtures.text = selectedProject!.mqOfFixtures != null ? selectedProject!.mqOfFixtures.toString() : "";
    // filterQualityOfTheArea.text = selectedProject!.qualityOfTheArea ?? "";

  }

  @override
  void initState() {
    cityMarketZoneMap.forEach((key, value) {
      value.sort();
      value.map((zone)=>zone.toTitleCase());
    });
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
    super.initState();
    if (selectedProject != null) {
      initializeControllers();
    }
    getFirestoreImages("${appConfig.COLLECT_REPORT_ACQUIRENTE}/");
  }

  @override
  void dispose() {
    super.dispose();
    clearData();
  }

  clearData (){
    filterObjective.clear();
    filterMarketZone.clear();
    filterGSF.clear();
    filterRooms.clear();
    filterBathrooms.clear();
    filterUnitFloor.clear();
    filterListingPrice.clear();
    filterPropertyType.clear();
    filterDescription.clear();
    filterDescriptionNeighborhood.clear();
    characteristicsMap.clear();
    filterEnergyClass.clear();
    filterConstructionYear.clear();
    filterSpecialHints.clear();
    filterPropertyUpForSaleAnswer.clear();
    filterStato.clear();
    filterStateOfFixtures.clear();
    filterVirtualTour.clear();
    filterAgencyCost.clear();
    filterNotaryCost.clear();
    filterRegistrationTax.clear();
    filterTipologiaStabile.clear();
    filterTotalPlans.clear();
    filterCondominiumExpenses.clear();
    filterHeating.clear();
    filterHeatingCosts.clear();
    filterStableConditions.clear();
    selectedProject = ReportAcquirente.empty();

  }

  @override
  void didUpdateWidget(BuyerReportDataInsertionPopup oldWidget) {
    super.didUpdateWidget(oldWidget);
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
  }

  Widget selectFooter() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialFooter();
    }else if(widget.isFromEdit){
      return editFooter();
    } else {
      return standardFooter();
    }
  }

  Widget initialFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          width: 150,
          child: BaseNewarcButton(
              buttonText: 'Inizia',
              onPressed: () async {
                // create ReportAcquirente project object
                final FirebaseFirestore _db = FirebaseFirestore.instance;
                ReportAcquirente _project = ReportAcquirente.empty();
                // assign agency
                _project.agencyId = widget.agencyUser.agencyId!;

                // save project to firestore
                DocumentReference projectResponse = await _db.collection(appConfig.COLLECT_REPORT_ACQUIRENTE).add(_project.toMap());
                print("projectResponse.id ====> ${projectResponse.id}");
                _project.id = projectResponse.id;
                setState(() {
                  selectedView = 'localizzazione';
                  selectedProject = _project;
                });
              }),
        )
      ],
    );
  }

  Widget standardFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        selectedView == 'localizzazione'
            ? SizedBox(width: 150)
            : ElevatedButton(
            onPressed: () => _backButtonSetStateFunction(),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              fixedSize: Size(150, 45),
              backgroundColor: Color(0xffe8e8e8),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Transform.rotate(
                angle: pi,
                child: SvgPicture.asset(
                  'assets/icons/arrow.svg',
                  height: 16,
                  color: const Color(0xff6a6a6a),
                ),
              ),
              NarFormLabelWidget(
                label: 'Indietro',
                textColor: const Color(0xff6a6a6a),
                fontWeight: '600',
                letterSpacing: .5,
              ),
            ])),
        selectedView == 'pagamento' || selectedView == 'final-data-entry'
            ? SizedBox(width: 150)
            : Row(
              children: [
                selectedView == "localizzazione" || selectedView == 'info-generali' ? SizedBox.shrink() :
                ElevatedButton(
                onPressed: (){
                  int currentIndex = viewsOrder.indexOf(selectedView);

                  for (int i = currentIndex + 1; i < viewsOrder.length; i++) {
                    final next = viewsOrder[i];

                    if(next == "info-sullo-stabile" && selectedProject!.propertyType != "Appartamento"){
                      continue;
                    }

                    if (next == 'stima-ristrutturazione' &&
                        (selectedProject!.objective != "Vendita" ||
                        selectedProject!.buyerReportStatus != "Da ristrutturare")) {
                      continue;
                    }

                    setState(() {
                      selectedView = next;
                    });
                    break;
                  }
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  fixedSize: Size(150, 45),
                  backgroundColor:Color(0xFFE0ECF9),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
                child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                  NarFormLabelWidget(
                    label: 'Salta',
                    textColor: Theme.of(context).primaryColor,
                    fontWeight: '600',
                    letterSpacing: .5,
                  ),
                  SvgPicture.asset(
                    'assets/icons/arrow.svg',
                    height: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ])),
                SizedBox(width: 15),
                ElevatedButton(
                onPressed: () => _forwardButtonConditions() ? _forwardButtonSetStateFunction() : _forwardButtonErrorFunction(),
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  fixedSize: Size(150, 45),
                  backgroundColor: Theme.of(context).primaryColor.withOpacity(_forwardButtonConditions() ? 1.0 : 0.3),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
                child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                  NarFormLabelWidget(
                    label: 'Avanti',
                    textColor: Colors.white,
                    fontWeight: '600',
                    letterSpacing: .5,
                  ),
                  SvgPicture.asset(
                    'assets/icons/arrow.svg',
                    height: 16,
                    color: Colors.white,
                  ),
                ])),
              ],
            ),
      ],
    );
  }

  Widget editFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ElevatedButton(
            onPressed: () => _forwardButtonConditions() ? saveFinalData(isForwardBtnSetState: true) : _forwardButtonErrorFunction(),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              fixedSize: Size(150, 45),
              backgroundColor: Theme.of(context).primaryColor.withOpacity(_forwardButtonConditions() ? 1.0 : 0.3),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child:NarFormLabelWidget(
              label: 'Salva',
              textColor: AppColor.white,
              fontWeight: '600',
              letterSpacing: .5,
        ))
      ],
    );
  }

  Future<void> saveFinalData({required bool isForwardBtnSetState})async{
    setState(() {
      _isFrozen = true;
    });
    try{
      if(isForwardBtnSetState){
        if (selectedView == 'caratteristiche') {
          if (characteristicsMap.values.any((value) => value == true)
              && ((selectedProject!.constructionYear != null)&&(selectedProject!.constructionYear != ""))
              && (selectedProject!.externalEsposition.isNotEmpty)) {
            saveCharacteristics();
          }
        }else if (selectedView == 'planimetria') {
          if (planimetryImages.isNotEmpty) {
            await savePlanimetryImages();
            setState(() {
              selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
            });
          }
        } else if (selectedView == 'fotografie-interne') {
          if (picturesImages.isNotEmpty && picturesImages.any((map) => map['type'] == "internal")) {
            if (!picturesImages.any((elem) => elem['tag'] == null)) {
              await savePicturesImages();
            }
          }
        }else if (selectedView == 'fotografie-esterne') {
          if (picturesImages.isNotEmpty && picturesImages.any((map) => map['type'] == "external")) {
            if (!picturesImages.any((elem) => elem['tag'] == null)) {
              await savePicturesImages();
            }
          }
        }
      }
      // else{
      //   if (selectedView == 'comparabili') {
      //     if ((selectedProject!.selectedComparabile?.any((item) => item.isSelected ?? false) ?? false)) {
      //       setState(() {
      //         selectedProject?.selectedComparabile = (allComparabile ?? []).where((val) => val.isSelected ?? false).toList();
      //       });
      //     }
      //   }
      // }
      try {
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
            .doc(selectedProject!.id)
            .set(selectedProject!.toMap());
      } catch (e) {
        print("Firestore SET failed: $e");
      }
      setState(() {
        _isFrozen = false;
      });
      Navigator.of(context).pop();
      widget.onClose(selectedProject!.id!);
    }catch(e){
      print("Error while saving data ${e.toString()}");
      setState(() {
        _isFrozen = false;
      });
      Navigator.of(context).pop();
      widget.onClose("");
    }
  }

  bool _forwardButtonConditions() {
    bool condition = false;
    if (selectedView == 'localizzazione') {
      condition = selectedProject!.localizzazione();
    } else if (selectedView == 'info-generali') {
      condition = selectedProject!.info_generaliForBuyerReport();
    }
    else if (selectedView == 'info-sullo-stabile') {
      condition = selectedProject!.info_sullo_stabileForBuyerReport();
    }else if (selectedView == 'riscaldamento-energetica') {
      condition = selectedProject!.heating_energyForBuyerReport();
    } else if (selectedView == 'descrizione') {
      condition = (selectedProject!.description != null) && (selectedProject!.description != "");
    } else if (selectedView == 'descrizione-del-quartiere') {
      condition = (selectedProject!.descriptionNeighborhood != null) && (selectedProject!.description != "");
    } else if (selectedView == 'caratteristiche') {
      condition = (characteristicsMap.values.any((value) => value == true)
          && ((selectedProject!.constructionYear != null)&&(selectedProject!.constructionYear != ""))
          && (selectedProject!.externalEsposition.isNotEmpty)
      );
    } else if (selectedView == 'prospetto-costi') {
      condition = (selectedProject!.prospettoCostiForBuyerReport());
    }else if (selectedView == 'planimetria') {
      condition = (planimetryImages.isNotEmpty);
    } else if (selectedView == 'fotografie-interne') {
      condition =  (picturesImages.isNotEmpty && picturesImages.every((map) => map['type'] != null) && picturesImages.every((map) => map['tag'] != null) && picturesImages.any((map) => map['type'] == "internal"));
    }else if (selectedView == 'fotografie-esterne') {
      condition =  (picturesImages.isNotEmpty && picturesImages.every((map) => map['type'] != null) && picturesImages.every((map) => map['tag'] != null) && picturesImages.any((map) => map['type'] == "external"));
    } else if (selectedView == 'virtual-tour') {
      condition =   ((selectedProject!.reportVirtualTour != null) && (selectedProject!.reportVirtualTour != ""));
    } else if (selectedView == 'stima-ristrutturazione') {
      condition =   selectedProject!.stimaRistrutturazione();
    }
    return condition;
  }

  void _backButtonSetStateFunction() {
    if (selectedView != 'localizzazione') {
      int currentIndex = viewsOrder.indexOf(selectedView);

      for (int i = currentIndex - 1; i >= 0; i--) {
        final previous = viewsOrder[i];

        if(previous == "info-sullo-stabile" && selectedProject!.propertyType != "Appartamento"){
          continue;
        }

        if (previous == 'stima-ristrutturazione' &&
            (selectedProject!.objective != "Vendita" ||
            selectedProject!.buyerReportStatus != "Da ristrutturare")) {
          continue; // Skip this one
        }

        setState(() {
          selectedView = previous;
        });
        break;
      }
    }
  }

  _forwardButtonErrorFunction() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return Center(
              child: BaseNewarcPopup(
                title: 'Errore',
                column: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: _errorMessage[selectedView],
                      textAlign: TextAlign.center,
                      fontWeight: '600',
                      letterSpacing: 1,
                    ),
                  ],
                ),
              ));
        });
  }

  _forwardButtonSetStateFunction() async {
    String? currentView = selectedView;
    if (currentView == 'localizzazione') {
      if (selectedProject!.localizzazione()) {
        setState(() {
          selectedProject?.isLocalizzazioneIncludeInBuyerReport = true;
          selectedView = getNextValidView(currentView!);
        });
      }
    } else if (currentView == 'info-generali') {
      if (selectedProject!.info_generaliForBuyerReport()) {
          setState(() {
            selectedProject?.isInformazioniGeneraliIncludeInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
      }
    }else if (currentView == 'info-sullo-stabile') {
          setState(() {
            selectedProject?.isInformazioniSulloStabileIncludeInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
    } else if (currentView == 'riscaldamento-energetica') {
          setState(() {
            selectedProject?.isHeatingAndEnergyInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
    } else if (currentView == 'descrizione') {
        setState(() {
          selectedProject?.isDescrizioneImmobileIncludeInBuyerReport = true;
          selectedView = getNextValidView(currentView!);
        });
    }  else if (currentView == 'descrizione-del-quartiere') {
        setState(() {
          selectedProject?.isDescrizioneDelQuartiereIncludeInBuyerReport = true;
          selectedView = getNextValidView(currentView!);
        });
    } else if (currentView == 'caratteristiche') {
      if (characteristicsMap.values.any((value) => value == true)
          && ((selectedProject!.constructionYear != null)&&(selectedProject!.constructionYear != ""))
          && (selectedProject!.externalEsposition.isNotEmpty)) {
        saveCharacteristics();
        setState(() {
          selectedProject?.isCaratteristicheInBuyerReport = true;
          selectedView = getNextValidView(currentView!);
        });
      }
    } else if (currentView == 'prospetto-costi') {
      if (selectedProject!.prospettoCostiForBuyerReport()) {
        setState(() {
          selectedProject?.isProspettoCostiInBuyerReport = true;
          selectedView = getNextValidView(currentView!);
        });
      }
    } else if (currentView == 'planimetria') {
      if (planimetryImages.isNotEmpty) {
        await savePlanimetryImages();
        setState(() {
          selectedProject?.isPlanimetriaCatastaleInBuyerReport = true;
          selectedView = getNextValidView(currentView!);
        });
      }
    } else if (currentView == 'fotografie-interne') {
        if (picturesImages.isNotEmpty && picturesImages.any((map) => map['type'] == "internal")) {
        if (!picturesImages.any((elem) => elem['tag'] == null)) {
          await savePicturesImages();
          setState(() {
            selectedProject?.isFotografieInterneInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
        }
      }
    }else if (currentView == 'fotografie-esterne') {
        if (picturesImages.isNotEmpty && picturesImages.any((map) => map['type'] == "external")) {
        if (!picturesImages.any((elem) => elem['tag'] == null)) {
          await savePicturesImages();
          setState(() {
            selectedProject?.isFotografieEsterneInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
        }
      }
    } else if (currentView == 'virtual-tour') {
      if ((selectedProject!.reportVirtualTour != null) && (selectedProject!.reportVirtualTour != "")) {
          setState(() {
            selectedProject?.isVirtualTourInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
      }
    }
    else if (currentView == 'stima-ristrutturazione') {
      if (selectedProject!.wantToIncludeEstimateForRenovation != null) {
          setState(() {
            selectedProject?.isStimaRistrutturazioneInBuyerReport = true;
            selectedView = getNextValidView(currentView!);
          });
      }
    }
    else if (currentView == 'final-data-entry') {
          setState(() {
            selectedView = getNextValidView(currentView!);
          });
    }

  }

  String? getNextValidView(String currentView) {
    int currentIndex = viewsOrder.indexOf(currentView);

    for (int i = currentIndex + 1; i < viewsOrder.length; i++) {
      final next = viewsOrder[i];

      if(next == "info-sullo-stabile" && selectedProject!.propertyType != "Appartamento"){
        continue;
      }

      if (next == 'stima-ristrutturazione' &&
          (selectedProject!.objective != "Vendita" ||
          selectedProject!.buyerReportStatus != "Da ristrutturare")) {
        continue;
      }

      return next;
    }

    return null;
  }

  void saveCharacteristics() {
    selectedProject!.elevator = characteristicsMap['Ascensore'];
    selectedProject!.hasCantina = characteristicsMap['Cantina'];
    selectedProject!.terrace = characteristicsMap['Terrazzo'];
    selectedProject!.hasConcierge = characteristicsMap['Portineria'];
    selectedProject!.highEfficiencyFrames = characteristicsMap['Infissi ad alta efficienza'];
    selectedProject!.doubleEsposition = characteristicsMap['Doppia esposizione'];
    selectedProject!.tripleEsposition = characteristicsMap['Tripla esposizione'];
    selectedProject!.quadrupleEsposition = characteristicsMap['Quadrupla esposizione'];
    selectedProject!.centralizedHeating = characteristicsMap['Riscaldamento centralizzato'];
    selectedProject!.autonomousHeating = characteristicsMap['Riscaldamento autonomo'];
    selectedProject!.privateGarden = characteristicsMap['Giardino privato'];
    selectedProject!.sharedGarden = characteristicsMap['Giardino condominiale'];
    selectedProject!.surveiledBuilding = characteristicsMap['Stabile videosorvegliato'];
    selectedProject!.nobleBuilding = characteristicsMap['Stabile signorile'];
    selectedProject!.fiber = characteristicsMap['Fibra ottica'];
    selectedProject!.airConditioning = characteristicsMap['Pred. condizionatore'];
    selectedProject!.securityDoor = characteristicsMap['Porta blindata'];
    selectedProject!.tvStation = characteristicsMap['Impianto TV'];
    selectedProject!.alarm = characteristicsMap['Pred. antifurto'];
    selectedProject!.motorizedSunblind = characteristicsMap['Tapparelle motorizzate'];
    selectedProject!.domotizedSunblind = characteristicsMap['Tapparelle domotizzate'];
    selectedProject!.domotizedLights = characteristicsMap['Luci domotizzate'];
    selectedProject!.highFloor = characteristicsMap['Piano alto'];
    selectedProject!.metroVicinity = characteristicsMap['Vicinanza metro'];
    selectedProject!.bigBalconies = characteristicsMap['Ampi balconi'];
    selectedProject!.bigLiving = characteristicsMap['Grande zona living'];
    selectedProject!.doubleBathroom = characteristicsMap['Doppi servizi'];
    selectedProject!.swimmingPool = characteristicsMap['Piscina'];
    selectedProject!.hasGarage = characteristicsMap['Box o garage'];
    selectedProject!.solarPanel = characteristicsMap['Fotovoltaico'];
    selectedProject!.walkInCloset = characteristicsMap['Cabina armadio'];
  }

  Future savePlanimetryImages() async {
    Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_REPORT_ACQUIRENTE}/${selectedProject!.id}/planimetrie');
    final listResult = await ref.listAll();
    for (var element in listResult.items) {
      await FirebaseStorage.instance.ref(element.fullPath).delete();
    }
    selectedProject!.planimetry.clear();
    for (int i = 0; i < planimetryImages.length; i++) {
      String pictureFilename = 'planimetrie/planimetry_${i + 1}' + p.extension(planimetryImages[i].name);
      await uploadImageToStorage(appConfig.COLLECT_REPORT_ACQUIRENTE, selectedProject!.id!, pictureFilename, planimetryImages[i]);
      selectedProject!.planimetry.add(pictureFilename);
    }
  }

  Future savePicturesImages() async {
    Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_REPORT_ACQUIRENTE}/${selectedProject!.id}/fotografie');
    final listResult = await ref.listAll();
    for (var element in listResult.items) {
      await FirebaseStorage.instance.ref(element.fullPath).delete();
    }
    selectedProject!.pictures.clear();
    for (int i = 0; i < picturesImages.length; i++) {
      String pictureFilename = 'fotografie/${picturesImages[i]['tag'] ?? 'notag'}_${i}' + p.extension(picturesImages[i]['file']!.name);
      await uploadImageToStorage(appConfig.COLLECT_REPORT_ACQUIRENTE, selectedProject!.id!, pictureFilename, picturesImages[i]['file']);
      selectedProject!.pictures.add({'tag': picturesImages[i]['tag'], 'file': pictureFilename,'type':picturesImages[i]['type']});
    }
  }

  Future<UploadTask?> uploadImageToStorage(String directory, String docId, String filename, XFile? file) async {
    if (file == null) {
      return null;
    }

    UploadTask uploadTask;

    Reference ref = FirebaseStorage.instance.ref().child(directory).child(docId).child(filename);

    final metadata = SettableMetadata(
      contentType: file.mimeType,
      customMetadata: {'picked-file-path': file.path},
    );

    uploadTask = ref.putData(await file.readAsBytes(), metadata);
    uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
      switch (taskSnapshot.state) {
        case TaskState.running:
          final progress = 100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
          // print("Upload is $progress% complete.");
          break;
        case TaskState.paused:
        // print("Upload is paused.");
          break;
        case TaskState.canceled:
        // print("Upload was canceled");
          break;
        case TaskState.error:
        // Handle unsuccessful uploads
          break;
        case TaskState.success:
        // Handle successful uploads on complete
        // ...
          break;
      }
    });

    return Future.value(uploadTask);
  }

  getFirestoreImages(directory) {
    if (selectedProject != null) {
      if (selectedProject!.pictures.isNotEmpty) {
        selectedProject!.pictures.forEach((pic) async {
          Reference ref = FirebaseStorage.instance.ref().child(directory + selectedProject!.id + "/" + pic['file']);
          Uint8List? data = await ref.getData();
          setState((){
            picturesImages.add({'tag': pic['tag'], 'file': XFile.fromData(data!, name: pic['file']),'type':pic["type"]});
          });
        });
      }
      if (selectedProject!.planimetry.isNotEmpty) {
        selectedProject!.planimetry.forEach((plan) async {
          Reference ref = FirebaseStorage.instance.ref().child(directory + selectedProject!.id + "/" + plan);
          Uint8List? data = await ref.getData();
          setState((){
            planimetryImages.add(XFile.fromData(data!, name: plan));
          });
        });
      }
    }
  }

  selectView() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialDialog();
    } else if (selectedView == 'localizzazione') {
      return localizzazioneDialog();
    } else if (selectedView == 'info-generali') {
      return infoGeneraliDialog();
    }else if (selectedView == 'info-sullo-stabile') {
      return infoSulloStabileDialog();
    }else if (selectedView == 'riscaldamento-energetica') {
      return heatingAndEnergyDialog();
    } else if (selectedView == 'descrizione') {
      return descrizioneDialog();
    }else if (selectedView == 'descrizione-del-quartiere') {
      return descrizioneDelQuartiereDialog();
    } else if (selectedView == 'caratteristiche') {
      return caratteristicheDialog();
    }else if (selectedView == 'prospetto-costi') {
      return prospettoCostiDialog();
    } else if (selectedView == 'planimetria') {
      return planimetriaDialog();
    } else if (selectedView == 'fotografie-interne') {
      return fotografieDialog(selectedView: "fotografie interne");
    } else if (selectedView == 'fotografie-esterne') {
      return fotografieDialog(selectedView: "fotografie esterne");
    } else if (selectedView == 'virtual-tour') {
      return virtualTourDialog();
    }else if (selectedView == 'comparabili') {
      return comparabiliDialog();
    } else if (selectedView == 'stima-ristrutturazione') {
      return stimaRistrutturazioneDialog();
    }
    else if (selectedView == 'final-data-entry') {
      return finalDataEntryDialog();
    } else {
      return Center(child: NarFormLabelWidget(label: "view $selectedView unknown"));
    }
  }

  Widget initialDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        NarFormLabelWidget(
          label: 'Sei pronto per effettuare \nun nuovo Report di Vendita?',
          overflow: TextOverflow.visible,
          textAlign: TextAlign.center,
          fontWeight: '800',
          fontSize: 30,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
          height: MediaQuery.of(context).size.height / 3.5,
          width: 600,
          decoration: BoxDecoration(color: const Color.fromARGB(255, 234, 234, 234), borderRadius: BorderRadius.all(Radius.circular(15))),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 10,
              ),
              NarFormLabelWidget(
                label: 'COSA TI SERVIRÀ',
                fontWeight: '700',
                fontSize: 14,
                textColor: Color(0xff5f5f5f),
                letterSpacing: 2,
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/dati-immobile.png',
                          color: Color(0xFF5DB1E3),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Dati immobile',
                          fontWeight: '500',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Theme.of(context).primaryColorLight,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/planimetria.png',
                          color: Color(0xFF5DB1E3),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Planimetria',
                          fontWeight: '500',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: 'Opzionale',
                          fontWeight: '500',
                          fontSize: 10,
                          textColor: Color(0xff878787),
                          letterSpacing: 1,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/fotografie.png',
                          color: Color(0xFF5DB1E3),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Fotografie',
                          fontWeight: '500',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: 'Opzionale',
                          fontWeight: '500',
                          fontSize: 10,
                          textColor: Color(0xff878787),
                          letterSpacing: 1,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        Column(children: [
          NarFormLabelWidget(
            label: 'Non hai tutto il materiale a disposizione?',
            fontWeight: '500',
            fontSize: 16,
            textColor: Theme.of(context).disabledColor,
          ),
          SizedBox(
            height: 5,
          ),
          NarFormLabelWidget(
            label: 'Non preoccuparti: potrai aggiungere in seguito il materiale mancante.',
            fontWeight: '500',
            fontSize: 14,
            textColor: Theme.of(context).disabledColor,
            letterSpacing: 0,
          )
        ]),
      ],
    );
  }

  Widget localizzazioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Localizzazione',
          fontWeight: '800',
          fontSize: 23,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
            width: 500,
            // height: MediaQuery.of(context).size.height * 0.8,
            child: Column(
              children: [
                SizedBox(
                  width: 500,
                  child: AddressSearchBar(
                    label: 'Indirizzo',
                    onPlaceSelected: (selectedPlace) async {
                      if (BaseAddressInfo.fromMap(selectedPlace["place"]).isValidAddress()){
                        setState(() {
                          selectedProject!.addressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                        });
                      } else{
                        setState(() {
                          selectedProject!.addressInfo = null;
                        });
                      }
                    },
                    initialAddress: selectedProject!.addressInfo?.fullAddress,
                  ),
                ),
                (cityMarketZoneMap.containsKey(selectedProject!.addressInfo?.city)) ?
                SizedBox(
                  width: 500,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 12,),
                        NarFormLabelWidget(
                          label: 'Zona',
                          textColor: Color(0xff696969),
                          fontSize: 13,
                          fontWeight: '600',
                          textAlign: TextAlign.left,
                        ),
                        SizedBox(height: 5,),
                        SearchSelectBox(
                            maxLargeness: 500,
                            controller: filterMarketZone,
                            options: [
                              for (String value in cityMarketZoneMap[selectedProject!.addressInfo!.city] ?? []) {'id': value, 'name': value.toTitleCase()}
                            ],
                            onSelection: (selected) {
                              setState(() {
                                selectedProject!.marketZone = selected;
                              });
                            }
                        )
                      ]
                  ),
                )
                    : SizedBox(),
              ],
            )
        ),
      ],
    );
  }

  Widget infoGeneraliDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Informazioni generali',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
            width: MediaQuery.of(context).size.width * 0.70,
            padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.05),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
              children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Categoria",
                      options: ['Residenziale','Commerciale'],
                      controller: filterCategory,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.category = filterCategory.text;
                        });
                      }),
                    ),
                  ),
                  SizedBox(width: 10),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Tipologia",
                      options: appConst.propertyTypesList,
                      controller: filterPropertyType,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.propertyType = filterPropertyType.text;
                        });
                      }),
                    ),
                  ),

                ],
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: CustomTextFormField(
                      isNumber: true,
                      isExpanded: false,
                      suffixIcon: Container(
                        width: 14,
                        padding: const EdgeInsets.only(right: 10),
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: NarFormLabelWidget(label: "mq",textColor: AppColor.greyColor,fontWeight: '500',fontSize: 14,),
                        ),
                      ),
                      label: "Metri quadri commerciali",
                      controller: filterGSF,
                      onChangedCallback: (String _gSF) {
                        setState(() {
                          selectedProject!.grossSquareFootage = int.tryParse(filterGSF.text);
                        });
                      },
                    ),
                  ),
                  SizedBox(width: 10),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Locali",
                      options: const ["1", "2", "3", "4", "5+"],
                      controller: filterRooms,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.rooms = int.tryParse(filterRooms.text.replaceAll(r'+', ''));
                        });
                      }),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Bagni",
                      options: const ["1", "2", "3+"],
                      controller: filterBathrooms,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.numberOfBathrooms = int.tryParse(filterBathrooms.text.replaceAll(r'+', ''));
                        });
                      }),
                    ),
                  ),
                  SizedBox(width: 10),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Piano",
                      options: const ["Piano terra / rialzato", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10+"],
                      controller: filterUnitFloor,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.unitFloor = filterUnitFloor.text;
                        });
                      }),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Stato",
                      options: ["Nuovo / In Costruzione","Ottimo / Ristrutturato","Buono / Abitabile","Da ristrutturare"  ],
                      controller: filterStato,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.buyerReportStatus = filterStato.text;
                        });
                      }),
                    ),
                  ),
                  SizedBox(width: 10),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: NarSelectBoxWidget(
                      label: "Obiettivo",
                      options: ["Vendita","Affitto"],
                      controller: filterObjective,
                      onChanged: ((value) {
                        setState(() {
                          selectedProject!.objective = filterObjective.text;
                        });
                      }),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 350),
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Richiesta",
                        isMoney: true,
                        controller: filterListingPrice,
                        onChangedCallback: (String _price) {
                          setState(() {
                            selectedProject!.listingPrice = int.tryParse(_price.replaceAll('.', '').replaceAll(',', '.'));
                          });
                        },
                      ),
                    ),
                    SizedBox(width: 10),
                    ConstrainedBox(constraints: BoxConstraints(maxWidth: 350),child: Container(),)
                  ],
                ),
            ]
            )
        )
      ],
    );
  }

  Widget infoSulloStabileDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Informazioni sullo stabile',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
          child: Container(
              width: MediaQuery.of(context).size.width * 0.70,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 350),
                      child: NarSelectBoxWidget(
                        label: "Tipologia stabile",
                        options: ['Standard','Signorile'],
                        controller: filterTipologiaStabile,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.tipologiaStabile = filterTipologiaStabile.text;
                          });
                        }),
                      ),
                    ),
                    SizedBox(width: 10),
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 350),
                      child: NarSelectBoxWidget(
                        label: "Condizioni stabile",
                        options: ["Originale","Rinnovato",'Nuova costruzione'],
                        controller: filterStableConditions,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.stableCondition = filterStableConditions.text;
                          });
                        }),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 15),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 350),
                      child: NarSelectBoxWidget(
                        label: "Piani totali",
                        options: ['1','2','3','4','5','6','7','8','9','10'],
                        controller: filterTotalPlans,
                        onChanged: ((value) {
                          setState(() {
                            selectedProject!.totalPlans = filterTotalPlans.text;
                          });
                        }),
                      ),
                    ),
                    SizedBox(width: 10),
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 350),
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Spese condiominiali",
                        isMoney: true,
                        suffixIcon: Container(
                          width: 80,
                          padding: const EdgeInsets.only(right: 10),
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: NarFormLabelWidget(label: "€/mese",textColor: AppColor.greyColor,fontWeight: '500',fontSize: 14,),
                          ),
                        ),
                        controller: filterCondominiumExpenses,
                        onChangedCallback: (String _price) {
                          setState(() {
                            selectedProject!.condominiumExpenses = double.tryParse(_price.replaceAll('.', '').replaceAll(',', '.'));
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ]
              )
          ),
        )
      ],
    );
  }

  Widget heatingAndEnergyDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Riscaldamento ed Energia',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
          child: Container(
              width: MediaQuery.of(context).size.width * 0.70,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 350),
                          child: NarSelectBoxWidget(
                            label: "Riscaldamento",
                            options: const ["Autonomo", "Centralizzato", "Assente","Non dichiarato"],
                            controller: filterHeating,
                            onChanged: ((value) {
                              setState(() {
                                selectedProject!.heating = filterHeating.text;
                              });
                            }),
                          ),
                        ),
                        SizedBox(width: 10),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 350),
                          child: CustomTextFormField(
                            isExpanded: false,
                            suffixIcon: Container(
                              width: 80,
                              padding: const EdgeInsets.only(right: 10),
                              child: Align(
                                alignment: Alignment.centerRight,
                                child: NarFormLabelWidget(label: "€/anno",textColor: AppColor.greyColor,fontWeight: '500',fontSize: 14,),
                              ),
                            ),
                            label: "Spese di riscaldamento",
                            isMoney: true,
                            controller: filterHeatingCosts,
                            onChangedCallback: (String _price) {
                              setState(() {
                                selectedProject!.heatingCosts = double.tryParse(_price.replaceAll('.', '').replaceAll(',', '.'));
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 350),
                          child: NarSelectBoxWidget(
                            label: 'Classe energetica',
                            options: appConst.energyClassList,
                            controller: filterEnergyClass,
                            onChanged: ((value) {
                              setState(() {
                                selectedProject!.actualEnergyClass = filterEnergyClass.text;
                              });
                            }),
                          ),
                        ),
                        SizedBox(width: 10),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 350),
                          child: NarSelectBoxWidget(
                            label: "Stato infissi",
                            options: const ['Originale','Buono stato','Nuovi'],
                            controller: filterStateOfFixtures,
                            onChanged: ((value) {
                              setState(() {
                                selectedProject!.stateOfFixtures = filterStateOfFixtures.text;
                              });
                            }),
                          ),
                        ),
                      ],
                    ),
                  ]
              )
          ),
        )
      ],
    );
  }

  Widget prospettoCostiDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Prospetto costi',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
          child: Container(
              width: MediaQuery.of(context).size.width * 0.70,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 350),
                  child: CustomTextFormField(
                    isExpanded: false,
                      label: "Rendita catastale",
                      isMoney: true,
                      controller: filterAgencyCost,
                      onChangedCallback: (String _price) {
                        setState(() {
                          selectedProject!.landRegistryIncome = double.tryParse(_price.replaceAll('.', '').replaceAll(',', '.'));
                        });
                      },
                    ),
                ),
                  SizedBox(height: 15),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: CustomTextFormField(
                      label: "Imposta di registro",
                      isMoney: true,
                      isExpanded: false,
                      controller: filterRegistrationTax,
                      onChangedCallback: (String _price) {
                        setState(() {
                          selectedProject!.registrationTax = double.tryParse(_price.replaceAll('.', '').replaceAll(',', '.'));
                        });
                      },
                    ),
                  ),
                  SizedBox(height: 15),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 350),
                    child: CustomTextFormField(
                      isExpanded: false,
                      label: "Costi notarili stimati",
                      isMoney: true,
                      controller: filterNotaryCost,
                      onChangedCallback: (String _price) {
                        setState(() {
                          selectedProject!.notaryCost = double.tryParse(_price.replaceAll('.', '').replaceAll(',', '.'));
                        });
                      },
                    ),
                  ),
              ]
              )
          ),
        )
      ],
    );
  }

  Widget virtualTourDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Virtual Tour',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
          child: Container(
              // height: MediaQuery.of(context).size.height * 0.50,
              width: MediaQuery.of(context).size.width * 0.70,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  NarFormLabelWidget(
                    label: 'Se hai il link di un Virtual Tour inseriscilo qui sotto.',
                    fontWeight: '700',
                    fontSize: 20,
                  ),
                SizedBox(height: 35,),
                Center(
                  child: SizedBox(
                    width: 500,
                    child: CustomTextFormField(
                        isExpanded: false,
                        label: "Incolla link",
                        controller: filterVirtualTour,
                        onChangedCallback: (String _price) {
                          setState(() {
                            selectedProject!.reportVirtualTour = filterVirtualTour.text;
                          });
                        },
                    ),
                  ),
                ),
              ]
              )
          ),
        )
      ],
    );
  }

  Widget comparabiliDialog(){

  Completer<GoogleMapController> mapController = Completer();



  final String _mapStyle = '''
[
  {
    "featureType": "poi",
    "stylers": [
      { "visibility": "off" }
    ]
  },
  {
    "featureType": "transit",
    "stylers": [
      { "visibility": "off" }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      { "color": "#cccccc" }
    ]
  },
  {
    "featureType": "road",
    "elementType": "labels",
    "stylers": [
      { "visibility": "on" }
    ]
  },
  {
    "featureType": "landscape",
    "elementType": "geometry",
    "stylers": [
      { "color": "#f5f5f5" }
    ]
  }
]
''';

  Set<Marker> _markers = {};
  LatLng center = LatLng(45.0712, 7.6869);
    return StatefulBuilder(builder: (context,_setState) {

      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: 'Comparabili',
            fontWeight: '800',
            fontSize: 22,
            textColor: Theme.of(context).disabledColor,
          ),

          Container(
              height: MediaQuery.of(context).size.height * 0.75,
              width: MediaQuery.of(context).size.width * 0.70,
              padding: EdgeInsets.only(top: 35),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    allComparabile?.isNotEmpty ?? false ?
                    Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * 0.70,
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(width: 1,color: Color(0xFFC2C2C2))
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: ListView.separated(
                              itemCount: allComparabile?.length ?? 0,
                              scrollDirection: Axis.vertical,
                              shrinkWrap: true,
                              separatorBuilder: (context,index){
                                return SizedBox(height: 10,);
                              },
                              itemBuilder: (context,index){
                                double gsf = double.tryParse(allComparabile![index].gSF!) ?? 1.0;
                                double gsfPrice = allComparabile![index].price! / gsf ;
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    index == 0 ? Padding(
                                      padding: const EdgeInsets.only(bottom: 10),
                                      child: NarFormLabelWidget(
                                        label: 'Seleziona fino a 4 comparabili',
                                        fontWeight: '600',
                                        fontSize: 16,
                                      ),
                                    ) : SizedBox.shrink(),
                                    Container(
                                      height: 175,
                                      padding: EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                          border: Border.all(color: allComparabile![index].isSelected ?? false ? Theme.of(context).primaryColor : Color(0xFFDBDBDB),width: allComparabile![index].isSelected ?? false ? 3 : 1)
                                      ),
                                      child: Row(
                                             children: [
                                          Container(
                                            width: 194,
                                            height: 154,
                                            decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(10),
                                                image: DecorationImage(image: NetworkImage(allComparabile![index].picture ?? ""),fit: BoxFit.contain,onError: (e,s){
                                                  print("Image Error ==> ${e.toString()}");
                                                })
                                            ),
                                          ),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.end,
                                                     children: [
                                                    ElevatedButton(
                                                        onPressed: allComparabile!.length > 3 ? (){} : ()async{
                                                          setState(() {
                                                            allComparabile![index].isSelected = !allComparabile![index].isSelected!;
                                                          });
                                                          _setState(() {});
                                                        },
                                                        style: ElevatedButton.styleFrom(
                                                          elevation: 0,
                                                          fixedSize: Size(136, 32),
                                                          backgroundColor: allComparabile![index].isSelected ?? false ? Theme.of(context).primaryColor : Color(0xFFE8E8E8),
                                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                        ),
                                                        child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                                                          SvgPicture.asset(
                                                            'assets/icons/check.svg',
                                                            height: 10,
                                                            color: allComparabile![index].isSelected ?? false ? Colors.white : Colors.black,
                                                          ),
                                                          NarFormLabelWidget(
                                                            label: 'Seleziona',
                                                            textColor: allComparabile![index].isSelected ?? false ? Colors.white : Colors.black,
                                                            fontWeight: '500',
                                                            fontSize: 13,
                                                          ),
                                                        ])),
                                                  ],
                                                ),
                                                Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: '${localCurrencyFormatMain.format(allComparabile![index].price)}€',
                                                      textColor: allComparabile![index].isSelected ?? false ? Colors.white : Colors.black,
                                                      fontWeight: '500',
                                                      fontSize: 13,
                                                    ),
                                                    SizedBox(height: 10,),
                                                    NarFormLabelWidget(
                                                      label: '${localCurrencyFormatMain.format(double.tryParse(gsfPrice.toStringAsFixed(2)) ?? 0.0)} €/mq',
                                                      textColor: allComparabile![index].isSelected ?? false ? Colors.white : Colors.black,
                                                      fontWeight: '500',
                                                      fontSize: 13,
                                                    ),
                                                  ],
                                                ),
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Image.asset("assets/icons/pdf_icon_mg.png",height: 20,),
                                                        SizedBox(width: 10,),
                                                        NarFormLabelWidget(
                                                          label: allComparabile![index].gSF.toString() ?? "",
                                                          textColor: Colors.black,
                                                          fontWeight: '500',
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),

                                                    Row(
                                                      children: [
                                                        Image.asset("assets/icons/pdf_icon_room.png",height: 20,),
                                                        SizedBox(width: 10,),
                                                        NarFormLabelWidget(
                                                          label: allComparabile![index].rooms.toString() ?? "",
                                                          textColor: Colors.black,
                                                          fontWeight: '500',
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),

                                                    Row(
                                                      children: [
                                                        Image.asset("assets/icons/pdf_icon_bath.png",height: 20,),
                                                        SizedBox(width: 10,),
                                                        NarFormLabelWidget(
                                                          label: allComparabile![index].bathrooms.toString() ?? "",
                                                          textColor: Colors.black,
                                                          fontWeight: '500',
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),

                                                    Row(
                                                      children: [
                                                        Image.asset("assets/icons/pdf_icon_stairs.png",height: 20,),
                                                        SizedBox(width: 10,),
                                                        NarFormLabelWidget(
                                                          label: allComparabile![index].unitFloor.toString() ?? "",
                                                          textColor: Colors.black,
                                                          fontWeight: '500',
                                                          fontSize: 13,
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                )
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                          SizedBox(width: 10,),
                          Expanded(child: ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: SizedBox(
                              height: MediaQuery.of(context).size.height * 0.68,
                              child: GoogleMap(
                                style: _mapStyle,
                                key: ValueKey(_markers.hashCode + center.hashCode),
                                onMapCreated: (GoogleMapController controller) {
                                  // mapController = controller;
                                  mapController.complete(controller);
                                },
                                initialCameraPosition: CameraPosition(
                                  target: center,
                                  zoom: 14,
                                ),
                                mapType: MapType.normal,
                                markers: _markers,
                              ),
                            ),
                          ))
                        ],
                      ),
                    )
                        :
                    ElevatedButton(
                        onPressed: ()async{
                          try{
                            setState(() {
                              _isFrozen = true;
                            });
                            Map comparabiliData = await getImmobiliareComparabili(latitude: selectedProject!.addressInfo!.latitude!,longitude: selectedProject!.addressInfo!.longitude!,maintenanceStatus:selectedProject!.buyerReportStatus! ,gSF: selectedProject!.grossSquareFootage,rooms: selectedProject!.rooms,bathrooms: selectedProject!.numberOfBathrooms,residential: selectedProject!.category == "Residenziale" ? true : false);

                            if(comparabiliData["data"] != null && comparabiliData["data"] != {}){
                              print("comparabiliData ===> ${comparabiliData["data"]}");

                              List listComp = comparabiliData["data"]["comps"] as List;

                              print("listComp ===> ${listComp.length}");
                              //Set<Marker> _newMarkers = {};

                              LatLng newCenter = LatLng(45.0712, 7.6869);

                              List<ImmaginaComparabili> newCompList = [];

                              final BitmapDescriptor customIcon = await BitmapDescriptor.asset(ImageConfiguration.empty, "assets/icons/pdf_icon_filled_location.png");


                              for(int i = 0; i <  listComp.length;i++){
                                print("listComp[i] ===> ${listComp[i]}");
                                ImmaginaComparabili comparabile = ImmaginaComparabili.fromJson(listComp[i]);
                                if(i == 0) newCenter = LatLng(comparabile.latitude!, comparabile.longitude!);

                                _markers.add(Marker(
                                  markerId: MarkerId(comparabile.title!),
                                  position: LatLng(comparabile.latitude!, comparabile.longitude!),
                                  infoWindow: InfoWindow(title: comparabile.title!),
                                  icon: customIcon
                                ));
                                newCompList.add(comparabile);
                              }


                              print("newCompList ===> ${newCompList.length}");
                             // print("_newMarkers ===> ${_newMarkers.length}");
                              print("newCenter ===> ${newCenter}");
                              // print("mapController ===> ${mapController != null}");


                              _setState((){
                                center = newCenter;
                               // _markers.addAll(_newMarkers);
                              });



                              // mapController.animateCamera(
                              //   CameraUpdate.newCameraPosition(
                              //     CameraPosition(target: newCenter, zoom: 14),
                              //   ),
                              // );

                              print("_setState _newMarkers ===> ${_markers.length}");
                              print("_setState newCenter ===> ${center}");

                              setState(() {
                                allComparabile = newCompList;
                                _isFrozen = false;
                              });

                              print("allComparabile ===> ${allComparabile?.length}");
                            }
                          }catch(e){
                            print("Error while fetching Comparabile  ===> ${e.toString()}");
                            setState(() {
                              _isFrozen = false;
                            });
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          fixedSize: Size(203, 45),
                          backgroundColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                          NarFormLabelWidget(
                            label: 'Cerca comparabili',
                            textColor: Colors.white,
                            fontWeight: '600',
                            letterSpacing: .5,
                          ),
                          SvgPicture.asset(
                            'assets/icons/search.svg',
                            height: 16,
                            color: Colors.white,
                          ),
                        ])),


                  ]
              )
          )
        ],
      );
    });
  }

  Widget stimaRistrutturazioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Stima ristrutturazione',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
            width: MediaQuery.of(context).size.width * 0.70,
            height: MediaQuery.of(context).size.height * 0.70,
            alignment: Alignment.center,
            padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 30,
                ),
                NarFormLabelWidget(
                  label: 'Vorresti includere una nostra stima per la ristrutturazione? ',
                  fontWeight: '700',
                  fontSize: 20,
                  textColor: AppColor.black,
                ),
                SizedBox(height: 10,),
                SizedBox(
                  height: 60,
                  width: 130,
                  child: SiNoToggleButton(
                    checkBoxHeight: 20,
                    checkBoxWidth: 20,
                    key: UniqueKey(),
                    startingState: (selectedProject!.wantToIncludeEstimateForRenovation),
                    onStateChanged: (selection) {
                      setState(() {
                        selectedProject!.wantToIncludeEstimateForRenovation = selection;
                      });
                    },
                  ),
                ),
                SizedBox(height: 50,),
                Container(
                  height: 76,
                  width: 587,
                  decoration: BoxDecoration(
                    color: Color(0xFFEEF7FF),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: Row(
                    children: [
                      Image.asset("assets/icons/thumbs-up.png",height: 41),
                      SizedBox(width: 17,),
                      NarFormLabelWidget(
                        label: 'Fornire una stima di ristrutturazione aumenterà l’interesse dei clienti!',
                        fontWeight: '500',
                        fontSize: 15,
                        textColor: Color(0xFF4483BC),
                      ),
                    ],
                  ),
                )
            ]
            )
        )
      ],
    );
  }

  Widget finalDataEntryDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
            height: MediaQuery.of(context).size.height * 0.50,
            width: MediaQuery.of(context).size.width * 0.60,
            padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.1),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 30,
                ),
                NarFormLabelWidget(
                  label: 'Inserimento dati completato!',
                  fontWeight: '700',
                  fontSize: 20,
                  textColor: AppColor.black,
                ),
                SizedBox(height: 30),
                ElevatedButton(
                    onPressed: () => saveFinalData(isForwardBtnSetState: false),
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      fixedSize: Size(170, 45),
                      backgroundColor: Theme.of(context).primaryColor,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      NarFormLabelWidget(
                        label: 'Vai al Report',
                        textColor: Colors.white,
                        fontWeight: '600',
                        letterSpacing: .5,
                      ),
                      SvgPicture.asset(
                        'assets/icons/arrow.svg',
                        height: 16,
                        color: Colors.white,
                      ),
                    ])),

            ]
            )
        )
      ],
    );
  }

  Widget descrizioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Descrizione immobile',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Aggiungi una descrizione',
                textColor: Color(0xff696969),
                fontSize: 13,
                fontWeight: '600',
                textAlign: TextAlign.left,
              ),
              SizedBox(
                height: 5,
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 650,maxHeight: MediaQuery.of(context).size.height / 2),
                child: TextField(
                  maxLength: 900,
                  buildCounter: (
                      BuildContext context, {
                        required int currentLength,
                        required bool isFocused,
                        required int? maxLength,
                      }) {
                    final remaining = (maxLength ?? 0) - currentLength;
                    return Text(
                      '$remaining caratteri rimasti',
                      style: TextStyle(fontSize: 12, color: Colors.grey,fontFamily: 'Raleway-500',),
                    );
                  },
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.top,
                  minLines: null,
                  maxLines: null,
                  expands: true,
                  keyboardType: TextInputType.multiline,
                  controller: filterDescription,
                  onChanged: (desc) {
                    setState(() {
                      selectedProject!.description = filterDescription.text;
                    });
                  },
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                    fontFamily: 'Raleway-600',
                  ),
                  decoration: InputDecoration(
                      hintText: 'Scrivi qui la descrizione dell’immobile',
                      hintStyle: TextStyle(
                        color: Color.fromARGB(255, 150, 150, 150),
                        fontSize: 15,
                        fontFamily: 'Raleway-600',
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      )),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget descrizioneDelQuartiereDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Descrizione del quartiere',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Aggiungi una descrizione',
                textColor: Color(0xff696969),
                fontSize: 13,
                fontWeight: '600',
                textAlign: TextAlign.left,
              ),
              SizedBox(
                height: 5,
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 650,maxHeight: MediaQuery.of(context).size.height / 2),
                child: TextField(
                  maxLength: 240,
                  buildCounter: (
                      BuildContext context, {
                        required int currentLength,
                        required bool isFocused,
                        required int? maxLength,
                      }) {
                    final remaining = (maxLength ?? 0) - currentLength;
                    return Text(
                      '$remaining caratteri rimasti',
                      style: TextStyle(fontSize: 12, color: Colors.grey,fontFamily: 'Raleway-500',),
                    );
                  },
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.top,
                  minLines: null,
                  maxLines: null,
                  expands: true,
                  keyboardType: TextInputType.multiline,
                  controller: filterDescriptionNeighborhood,
                  onChanged: (desc) {
                    setState(() {
                      selectedProject!.descriptionNeighborhood = filterDescriptionNeighborhood.text;
                    });
                  },
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                    fontFamily: 'Raleway-600',
                  ),
                  decoration: InputDecoration(
                      hintText: 'Scrivi qui la descrizione del quartiere.',
                      hintStyle: TextStyle(
                        color: Color.fromARGB(255, 150, 150, 150),
                        fontSize: 15,
                        fontFamily: 'Raleway-600',
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      )),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget externalEspositionButton(String label){
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          side: BorderSide(
              color: selectedProject!.externalEsposition.contains(label)? Colors.transparent : Color(0xffdbdbdb)
          ),
          backgroundColor: selectedProject!.externalEsposition.contains(label)? Theme.of(context).primaryColor : Colors.transparent,
        ),
        onPressed: () {
          if (selectedProject!.externalEsposition.contains(label)) {
            setState(() {
              selectedProject!.externalEsposition.removeWhere((item) => item == label);
            });
          } else {
            setState(() {
              selectedProject!.externalEsposition.add(label);
            });
          }

        },
        child: Text(
          label,
          style: TextStyle(
            color: selectedProject!.externalEsposition.contains(label) ? Theme.of(context).unselectedWidgetColor : Theme.of(context).primaryColorDark,
            fontSize: 14,
            fontFamily: 'Raleway-600',
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget caratteristicheDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Caratteristiche',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: MediaQuery.of(context).size.height / 2,
                width: MediaQuery.of(context).size.width * .8,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Color(0xffdbdbdb),
                  ),
                ),
                child: NarCheckboxWidget(
                  label: 'characteristics',
                  values: characteristicsMap,
                  columns: 4,
                  fontSize: 13,
                  childAspectRatio: 7,
                ),
              ),
              SizedBox(height: 10,),
              Container(
                width: MediaQuery.of(context).size.width * 0.6,
                alignment: Alignment.center,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Esposizione',
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '500',
                          ),
                          SizedBox(height: 10,),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              externalEspositionButton('Nord'),
                              externalEspositionButton('Sud'),
                              externalEspositionButton('Ovest'),
                              externalEspositionButton('Est'),
                            ],
                          )
                        ]
                    ),
                    SizedBox(width: 25,),
                    SizedBox(
                      width: 180,
                      child: CustomTextFormField(
                        isExpanded: false,
                        label: "Anno di costruzione",
                        controller: filterConstructionYear,
                        onChangedCallback: (String? _streetNum) {
                          if (_streetNum == null || _streetNum.isEmpty || int.tryParse(_streetNum) == null) {
                            setState(() {
                              selectedProject!.constructionYear = null;
                            });
                          } else {
                            setState(() {
                              selectedProject!.constructionYear = int.tryParse(filterConstructionYear.text);
                            });
                          };
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty || int.tryParse(value) == null) {
                            return 'Inserisci un anno di costruzione valido';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  getPlanimetryFromGallery(BuildContext context,) async {
    bool wrongExtension = false;
    bool wrongSize = false;
    await FilePicker.platform
        .pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPlanimetryExtensions,
    )
        .then((filesList) {
      if (filesList != null) {
        filesList.files.forEach((file) {
          if (file.size < 10 * 1024 * 1024) {
            if (allowedPlanimetryExtensions.contains(file.extension)) {
              planimetryImages.add(file.xFile);
            } else {
              wrongExtension = true;
            }
          } else {
            wrongSize = true;
          }
        });
      }
      setState(() {});
      if (wrongExtension) {
        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Estensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di planimetrie con estensione: ${allowedPlanimetryExtensions.join(', ')}'),
                  ],
                ),
              ),
            );
          },
        );
      }
      if (wrongSize) {
        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Dimensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di planimetrie con dimensione massima 10 MB'),
                  ],
                ),
              ),
            );
          },
        );
      }
    });
  }

  _showUploadPlanimetryDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: GestureDetector(
                            onTap: () async {
                              await getPlanimetryFromGallery(context);
                              setState(() {});
                              Navigator.of(context).pop(true);
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius: BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  Widget planimetriaDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Planimetria',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(
          height: 30,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 60),
          child: Container(
            height: MediaQuery.of(context).size.height / 2,
            child: Column(
              children: [
                  SizedBox(
                    height: 20,
                  ),
                  // Image picker
                  Container(
                    height: (MediaQuery.of(context).size.height / 2.6),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Color(0xffd3d3d3),
                      ),
                    ),
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          padding: EdgeInsets.only(top: 20, bottom: 20, left: 8, right: 100),
                          child: Wrap(
                            spacing: 12,
                            runSpacing: 20,
                            children: planimetryImages
                                .map((imageFile) => FutureBuilder<Uint8List>(
                              future: imageFile.readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Stack(
                                        children: [
                                          ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: imageFile.name.split('.').last.toLowerCase() == 'dwg'
                                                ? Image.asset(
                                              'assets/icons/dwg.png',
                                              color: Color(0xffa6a6a6),
                                              width: 150,
                                              height: 150,
                                              fit: BoxFit.cover,
                                            )
                                                : imageFile.name.split('.').last.toLowerCase() == 'pdf'
                                                ? Image.asset(
                                              'assets/icons/pdf.png',
                                              color: Color(0xffa6a6a6),
                                              width: 150,
                                              height: 150,
                                              fit: BoxFit.cover,
                                            )
                                                : Image.memory(
                                              snapshot.data!,
                                              width: 150,
                                              height: 150,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                          Positioned(
                                              top: 3,
                                              right: 3,
                                              child: GestureDetector(
                                                  onTap: () {
                                                    planimetryImages.remove(imageFile);
                                                    setState(() {});
                                                  },
                                                  child: Container(
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        color: Theme.of(context).primaryColorDark,
                                                      ),
                                                      child: Padding(
                                                          padding: EdgeInsets.all(3),
                                                          child: SvgPicture.asset(
                                                            'assets/icons/close-popup.svg',
                                                            height: 10,
                                                            color: Theme.of(context).unselectedWidgetColor,
                                                          )
                                                      )
                                                  )
                                              )
                                          ),
                                        ],
                                      ),
                                    ],
                                  );
                                } else if (snapshot.connectionState == ConnectionState.waiting) {
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    color: Colors.grey[300],
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                } else {
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    color: Colors.grey[300],
                                    child: Center(
                                      child: Icon(Icons.error, color: Colors.red),
                                    ),
                                  );
                                }
                              },
                            )
                            ).toList(),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: BaseNewarcButton(
                            color: Color(0xffE5E5E5),
                            textColor: Colors.black,
                            buttonText: "Carica",
                            fontWeight: '700',
                            fontSize: 14,
                            height: 35,
                            onPressed: () async {
                              await _showUploadPlanimetryDialog(context);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Future<void> getPictureFromGallery(BuildContext context,String selectedView) async {
    bool wrongExtension = false;
    final maxLimit = selectedView == "fotografie interne" ? 9 : selectedView == "fotografie esterne" ? 3 : 1000;
    final existingCount = picturesImages.where((item) => item["type"] == (selectedView == "fotografie esterne" ? "external" : "internal")).length;

    final filesList = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPicturesExtensions,
    );

    if (filesList != null) {
      final remainingSlots = maxLimit - existingCount;
      final selectedFiles = filesList.files.take(remainingSlots);
      for (final file in selectedFiles) {

        if (allowedPicturesExtensions.contains(file.extension?.toLowerCase())) {
          final bytes = await file.xFile.readAsBytes();
          Uint8List convertedBlob = bytes;

          if (file.extension?.toLowerCase().contains("heic") ?? false) {
            convertedBlob = await heicToJpeg.HeicToJpegService().convertHeicToJpeg(bytes);
          }

          picturesImages.add({
            'tag': null,
            'file': XFile.fromData(
              convertedBlob,
              name: file.name,
              mimeType: 'image/jpeg',
            ),
            'type': selectedView == "fotografie esterne" ? "external" : selectedView == "fotografie interne" ? "internal" : ""
          });
        } else {
          wrongExtension = true;
        }
      }
      setState(() {});
      if (wrongExtension) {
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Estensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di fotografie con estensione: ${allowedPicturesExtensions.join(', ')}'),
                  ],
                ),
              ),
            );
          },
        );
      }
    }
  }

  _showUploadPicturesDialog(BuildContext context,String selectedView,VoidCallback checkImageLimit) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: GestureDetector(
                            onTap: () async {
                              await getPictureFromGallery(context,selectedView);
                              setState(() {});
                              checkImageLimit();
                              Navigator.of(context).pop(true);
                              
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius: BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  Widget fotografieDialog({required String selectedView}) {

    return StatefulBuilder(builder: (context,_setState){
      final isPhotoEmpty = () => selectedView == "fotografie interne"
          ? picturesImages.any((item) => item["type"] == "internal")
          : picturesImages.any((item) => item["type"] == "external");

      final isCrossMaxLimit = () => selectedView == "fotografie interne"
          ? picturesImages.where((item) => item["type"] == "internal").length > 8
          : picturesImages.where((item) => item["type"] == "external").length > 2;
      void checkMaxLimit(){
        _setState((){});
      }
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          NarFormLabelWidget(
            label: selectedView.toCapitalized(),
            fontWeight: '800',
            fontSize: 22,
            textColor: Theme.of(context).disabledColor,
          ),
          SizedBox(
            height: 20,
          ),
          Container(
            height: MediaQuery.of(context).size.height / 1.40,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Color(0xffd3d3d3),
              ),
            ),
            child: Stack(
              children: [
                !(isPhotoEmpty()) ?
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: NarFormLabelWidget(
                        label: selectedView == "fotografie interne" ? 'Carica fino a un massimo di 9 foto' : selectedView == "fotografie esterne" ? "Carica fino a un massimo di 3 foto" : "",
                        fontSize: 18,
                        fontWeight: '600',
                        textColor: Color(0xFF9A9A9A),
                      ),
                    ),
                  ),
                )
                    :
                SingleChildScrollView(
                  padding: EdgeInsets.only(top: 10, bottom: 10, left: 8, right: 100),
                  child: Wrap(
                    spacing: 12,
                    runSpacing: 20,
                    children: picturesImages.where((imageFile) {
                      if (selectedView == "fotografie interne") {
                        return imageFile['type'] == 'internal';
                      } else if (selectedView == "fotografie esterne") {
                        return imageFile['type'] == 'external';
                      }
                      return true; // include all if no filter applies
                    }).map((imageFile) => FutureBuilder<Uint8List>(
                      future: imageFile['file'].readAsBytes(),
                      builder: (context, snapshot) {
                        TextEditingController tempController = TextEditingController(text: imageFile['tag'] ?? "");
                        if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Stack(
                                children: [
                                  MouseRegion(
                                    cursor: SystemMouseCursors.click,
                                    child: GestureDetector(
                                      onTap: () {
                                        showEnlargedDownloadableImage(imageFile);
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: Image.memory(
                                          snapshot.data!,
                                          width: 150,
                                          height: 150,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                      top: 3,
                                      right: 3,
                                      child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              picturesImages.remove(imageFile);
                                              checkMaxLimit();
                                            });
                                          },
                                          child: Container(
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Theme.of(context).primaryColorDark,
                                              ),
                                              child: Padding(
                                                  padding: EdgeInsets.all(3),
                                                  child: SvgPicture.asset(
                                                    'assets/icons/close-popup.svg',
                                                    height: 10,
                                                    color: Theme.of(context).unselectedWidgetColor,
                                                  )
                                              )
                                          )
                                      )
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              Container(
                                width: 150,
                                child: NarSelectBoxWidget(
                                  label: 'Seleziona ambiente',
                                  controller: tempController,
                                  onChanged: (value) {
                                    if (tempController.text != ''){
                                      imageFile['tag'] = tempController.text;
                                    }
                                    setState(() {});
                                  },
                                  options: List.of(appConst.roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                                  contentPadding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 2),
                                ),
                              ),
                            ],
                          );
                        } else if (snapshot.connectionState == ConnectionState.waiting) {
                          return Center(
                            child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                          );
                        } else {
                          return Container(
                            width: 80,
                            height: 80,
                            color: Colors.grey[300],
                            child: Center(
                              child: Icon(Icons.error, color: Colors.red),
                            ),
                          );
                        }
                      },
                    ))
                        .toList(),
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: BaseNewarcButton(
                    color: Color(0xffE5E5E5).withOpacity( isCrossMaxLimit() ? 0.3 : 1.0),
                    textColor: Colors.black.withOpacity( isCrossMaxLimit() ? 0.3 : 1.0),
                    buttonText: "Carica",
                    fontWeight: '700',
                    fontSize: 14,
                    height: 35,
                    onPressed: isCrossMaxLimit() ? (){} : () async {
                      await _showUploadPicturesDialog(context,selectedView,checkMaxLimit);
                    },
                  ),
                ),
              ],
            ),
          )
        ],
      );
    });
  }

  Future<Size> _calculateImageDimension(BuildContext context, String url) {
    Completer<Size> completer = Completer();
    Image image = Image.network(url);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(Size(
          info.image.width.toDouble(),
          info.image.height.toDouble(),
        ));
      }),
    );
    return completer.future;
  }

  downloadPictureFile(String url, String filename) {
    html.AnchorElement anchorElement = new html.AnchorElement(href: url);
    String nome_file = filename.replaceAll("fotografie/", "");
    anchorElement.download = nome_file;
    anchorElement.target = '_blank';
    anchorElement.click();
  }

  showEnlargedDownloadableImage(Map<String, dynamic> imageFile) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) {
        // builder: (context, setState) {
        return FutureBuilder<Size>(
          future: _calculateImageDimension(context, imageFile['file'].path),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      CircularProgressIndicator(
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(height: 5),
                      NarFormLabelWidget(
                        label: 'Loading...',
                        textColor: Colors.white,
                      )
                    ],
                  ));
            } else if (snapshot.hasError) {
              return AlertDialog(
                title: NarFormLabelWidget(label: 'Error'),
                content: NarFormLabelWidget(label: 'Could not load image'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: NarFormLabelWidget(label: 'Close'),
                  ),
                ],
              );
            } else {
              final size = snapshot.data!;

              double imageWidth = 0;
              double imageHeight = 0;
              double displayWidth = 0;
              double displayHeight = 0;

              double maxImageWidth = MediaQuery.of(context).size.width;
              double maxImageHeight = MediaQuery.of(context).size.height;

              imageWidth = size.width.toDouble();
              imageHeight = size.height.toDouble();
              double aspectRatio = imageWidth / imageHeight;

              displayWidth = imageWidth;
              displayHeight = imageHeight;

              if (displayWidth > maxImageWidth) {
                displayWidth = maxImageWidth;
                displayHeight = displayWidth / aspectRatio;
              }

              if (displayHeight > maxImageHeight) {
                displayHeight = maxImageHeight;
                displayWidth = displayHeight * aspectRatio;
              }

              return Center(
                child: Wrap(
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: Center(
                        child: Stack(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                    width: displayWidth,
                                    height: displayHeight,
                                    padding: const EdgeInsets.all(0),
                                    // decoration: BoxDecoration(
                                    //   borderRadius: BorderRadius.circular(15.0),
                                    // ),

                                    child: ListView(
                                      padding: EdgeInsets.all(0),
                                      shrinkWrap: true,
                                      children: [
                                        Card(
                                          color: const Color.fromRGBO(255, 255, 255, 1),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15.0),
                                          ),
                                          clipBehavior: Clip.hardEdge,
                                          child: Column(
                                            children: [
                                              Container(
                                                color: const Color.fromARGB(255, 228, 228, 228),
                                                width: displayWidth,
                                                height: displayHeight,
                                                child: Image.network(
                                                  imageFile['file'].path,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    )),
                              ],
                            ),
                            Positioned(
                              top: 10,
                              right: 70,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        downloadPictureFile(imageFile['file'].path, imageFile['file'].name);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.download,
                                        color: Colors.black,
                                      )),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.close,
                                        color: Colors.black,
                                      )),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        );
      },
    );
  }

  Widget indicazioniSpecialiDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Altre indicazioni',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(height: 30),
        Container(
          height: MediaQuery.of(context).size.height / 1.7,
          width: MediaQuery.of(context).size.width / 2.5,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Hai indicazioni speciali da darci riguardo al progetto?",
                style: TextStyle(
                  fontFamily: 'Raleway-700',
                  fontSize: 15,
                  color: Theme.of(context).disabledColor,
                ),
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 5),
              Text("Es: la colonna della sala è portante",
                  style: TextStyle(
                    fontFamily: 'Raleway-500',
                    fontSize: 13,
                    color: Theme.of(context).primaryColorLight,
                  )),
              SizedBox(height: 15),
              SiNoToggleButton(
                startingState: selectedProject!.hasSpecialHints ?? false,
                onStateChanged: (value) {
                  setState(() {
                    selectedProject!.hasSpecialHints = value;
                  });
                },
              ),
              SizedBox(height: 30),
              Container(
                child: CustomTextFormField(
                  isExpanded: false,
                  label: 'Scrivici qui le tue indicazioni speciali',
                  controller: filterSpecialHints,
                  onChangedCallback: (value) {
                    setState(() {
                      selectedProject!.specialHints = value;
                    });
                  },
                  enabled: selectedProject!.hasSpecialHints ?? false,
                  fillColor: (selectedProject!.hasSpecialHints ?? false) ? Theme.of(context).unselectedWidgetColor : Colors.grey[100],
                  minLines: 6,
                ),
              ),
              SizedBox(height: 30),
              NarFormLabelWidget(
                label: "Quando vorresti mettere in vendita l’immobile?",
                fontSize: 15,
                fontWeight: '700',
                textColor: Theme.of(context).disabledColor,
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 10),
              SizedBox(
                height: 75,
                child: NarSelectBoxWidget(
                  label: "Seleziona una risposta",
                  options: [
                    "L'immobile è gia in vendita",
                    "Appena pronto il progetto Immagina",
                    "Entro un mese",
                    "Entro tre mesi",
                  ],
                  onChanged: (value) {
                    if(value != null && value.toString().isNotEmpty){
                      setState(() {
                        selectedProject!.propertyUpForSaleAnswer = value;
                      });
                    }
                  },
                  controller: filterPropertyUpForSaleAnswer,
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: EdgeInsets.zero,
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Material(
          color: Colors.white,
          child: Stack(
            children: [
              Positioned(
                top: 18,
                right: 20,
                child: Container(
                  height: 20,
                  width: 20,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                        child: SvgPicture.asset(
                          'assets/icons/close-popup.svg',
                          width: 18,
                          color: Color(0xffb3b3b3),
                        ),
                        onTap: () {
                          if(widget.isFromEdit){
                            Navigator.of(context).pop();
                          }else if (selectedView == 'initial') {
                            Navigator.of(context).pop();
                          } else if(selectedView == "localizzazione" || selectedView == "info-generali"){
                            showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return Center(
                                      child: Container(
                                        width: 400,
                                        child: BaseNewarcPopup(
                                          title: 'La procedura non sarà salvata.',
                                          noButton: true,
                                          titleTextAlign: TextAlign.center,
                                          isShowCloseIcon: false,
                                          column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                                            return Column(
                                              mainAxisAlignment: MainAxisAlignment.end,
                                              children: [
                                                BaseNewarcButton(
                                                  buttonText: 'Rimani',
                                                  onPressed: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  textColor: Colors.white,
                                                  color: Theme.of(context).primaryColor,
                                                ),
                                                SizedBox(
                                                  height: 15,
                                                ),
                                                BaseNewarcButton(
                                                  buttonText: 'Chiudi',
                                                  onPressed: () async {
                                                    /// delete intl project
                                                    await FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE).doc(selectedProject!.id).delete();

                                                    Navigator.of(context).pop();
                                                    Navigator.of(context).pop();

                                                  },
                                                  textColor: Theme.of(context).primaryColor,
                                                  color: Colors.white,
                                                )
                                              ],
                                            );
                                          }),
                                        ),
                                      ));
                                });
                          } else {
                            showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return Center(
                                      child: Container(
                                        width: 400,
                                        child: BaseNewarcPopup(
                                          title: 'Vuoi davvero uscire \ndalla procedura?',
                                          noButton: true,
                                          isShowCloseIcon: false,
                                          column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                                            return Column(
                                              mainAxisAlignment: MainAxisAlignment.end,
                                              children: [
                                                ...!_isFrozen ? [BaseNewarcButton(
                                                  buttonText: 'Rimani',
                                                  onPressed: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  textColor: Colors.white,
                                                  color: Theme.of(context).primaryColor,
                                                  disableButton: _isFrozen,
                                                ),
                                                  SizedBox(
                                                    height: 15,
                                                  ),
                                                  BaseNewarcButton(
                                                    buttonText: 'Salva e chiudi',
                                                    onPressed: () async {
                                                      setState(() {
                                                        _isFrozen = true;
                                                        loading = true;
                                                      });
                                                      // save to selectedProject features not saved onChange
                                                      saveCharacteristics();
                                                      await savePlanimetryImages();
                                                      await savePicturesImages();
                                                      // save selectedProject to firestore
                                                      await FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE).doc(selectedProject!.id).set(selectedProject!.toMap());
                                                      setState(() {
                                                        _isFrozen = false;
                                                        loading = false;
                                                      });
                                                      Navigator.of(context).pop();
                                                      Navigator.of(context).pop();
                                                      widget.onClose(selectedProject!.id!);
                                                    },
                                                    textColor: Theme.of(context).primaryColor,
                                                    color: Colors.white,
                                                    disableButton: _isFrozen,
                                                  )]
                                                    : [Center(child: CircularProgressIndicator())]
                                              ],
                                            );
                                          }),
                                        ),
                                      ));
                                });
                          }
                        }),
                  ),
                ),
              ),
              Form(
                key: _formKey,
                child: Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 15,),
                            child: NarFormLabelWidget(
                              label: 'Nuovo Report Acquirente',
                              textColor: Color(0xFF565656),
                              fontSize: 18,
                              fontWeight: '600',
                            ),
                          ),
                          Container(
                            height: MediaQuery.of(context).size.height * 0.80,
                            padding: const EdgeInsets.only(left: 40.0, right: 40.0, top: 25,),
                            child: selectView(),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Container(
                              alignment: Alignment.bottomCenter,
                              padding: EdgeInsets.only(bottom: 50),
                              width: MediaQuery.of(context).size.width * 0.95,  child: Center(child: selectFooter())),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              formErrorMessage.length == 0 || formErrorMessage[0] == ''
                                  ? Container()
                                  : NarFormLabelWidget(
                                label: formErrorMessage.length > 0 ? formErrorMessage.join('') : '',
                                fontSize: 12,
                                fontWeight: 'bold',
                              ),
                            ],
                          ),
                        ],
                      ),


                    ],
                  ),
                ),
              ),
              if (_isFrozen)
                Positioned.fill(
                  child: Container(
                    color: Colors.black54,
                    child: Center(
                      child: !loading ? CircularProgressIndicator() : null,
                    ), // Semi-transparent overlay
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

