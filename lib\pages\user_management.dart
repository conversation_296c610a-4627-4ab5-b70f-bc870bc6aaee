import 'package:flutter/scheduler.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/pages/404_page.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_fonts/google_fonts.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom-button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:url_launcher/url_launcher.dart';


class UserManagementPage extends StatefulWidget {
  UserManagementPage({Key? key}) : super(key: key);

  static const String route = '/usermgmt';

  @override
  _UserManagementPageState createState() => _UserManagementPageState();
}

class _UserManagementPageState extends State<UserManagementPage> {
  // String email = "";
  // String password = "";
  // User? user;
  bool loading = true;
  bool verification_waiting = false;
  bool isVerified = false;
  bool isReset = false;
  String responseText = "";
  bool _obscureText = true;
  // bool pwdVisible = true;
  // final _formKey = GlobalKey<FormState>();

  late String mode;
  late String lang;
  late String oobCode;
  late String continueUrl;
  String? newPassword;
  bool pwdVisible = false;
  final _resetPasswordformKey = GlobalKey<FormState>();
  // RegExp originRegExp = RegExp(r'origin=app');
  final originRegExp = RegExp(r'origin=(app|website)');


  @override
  void initState() {
    mode = Uri.base.queryParameters["mode"] ?? '';
    lang = Uri.base.queryParameters["lang"] ?? '';
    oobCode = Uri.base.queryParameters["oobCode"] ?? '';
    continueUrl = Uri.base.queryParameters["continueUrl"] ?? '';
    // checkLoggedUser();
    checkURL();
    super.initState();
  }

  checkURL() {
    bool error = false;

    if (mode == '' || oobCode == '') error = true;
    if (mode == 'verifyEmail' && continueUrl == '') error = true;

    if (error) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushNamed(
          Page404.route,
        );
      });
    }
  }

  processVerification(BuildContext context) async {
    String mode = Uri.base.queryParameters["mode"] ?? '';
    String lang = Uri.base.queryParameters["lang"] ?? '';
    String oobCode = Uri.base.queryParameters["oobCode"] ?? '';
    String continueUrl = Uri.base.queryParameters["continueUrl"] ?? '';

    Map<String, dynamic> verificationParams = {
      'mode': mode,
      'oobCode': oobCode,
      'continueUrl': continueUrl,
      'lang': lang
    };

    bool verification_response = await verifyUserEmail(verificationParams);
    if (verification_response == true) {
      isVerified = true;
      responseText = "Your account is verified now!";
    } else {
      isVerified = true;
      
      responseText = "There was problem in verification. Try later";
    }

    setState(() {
      // loading = false;
    });
  }

  Future<bool> processPasswordReset(BuildContext context) async {
    String mode = Uri.base.queryParameters["mode"] ?? '';
    String lang = Uri.base.queryParameters["lang"] ?? '';
    String oobCode = Uri.base.queryParameters["oobCode"] ?? '';
    String continueUrl = Uri.base.queryParameters["continueUrl"] ?? '';

    Map<String, dynamic> verificationParams = {
      'mode': mode,
      'oobCode': oobCode,
      'continueUrl': continueUrl,
      'lang': lang,
      'newPassword': newPassword
    };

    setState(() {
      loading = false;
    });

    return await verifyUserEmail(verificationParams);
  }

  
  Widget verificationDisplay = SizedBox();
  bool hasVerifiyResponse = false;
  
  verifyButton(context) {
    dynamic callback = () async {

      setState(() {
        verification_waiting = true;
      });

      await processVerification(context);

      setState(() {
        verification_waiting = false;
        hasVerifiyResponse = true;
      });

      if (isVerified == true) {
        /*showDialog<void>(
          context: context,
          barrierDismissible: false, // user must tap button!
          builder: (BuildContext context) {
            return verificationResponseDialog(
                context,
                ['assets/check.png', 70, 70],
                'Account verificato!',
                'Inizia subito a collaborare con noi e scopri tutti i vantaggi!',
                true);
          },
        );*/
        final _match = originRegExp.firstMatch(continueUrl);
        
        verificationDisplay = Column(
          children: [
            SvgPicture.asset('assets/email-verified.svg', height: 45, width: 45,),
            SizedBox(height: 15,),
            NarFormLabelWidget(
              label: 'Email verificata!',
              fontSize: 22,
              textColor: Colors.black,
            ),
            SizedBox(height: 5,),
            NarFormLabelWidget(
              label: "Puoi tornare a utilizzare i servizi Newarc.",
              fontSize: 17,
              fontWeight: '500',
              letterSpacing: 0.2,
              textColor: Color(0xff686868),
            ),
            SizedBox(height: 35,),
            // ui_button(context, 'Conferma email', callback, 'primary', 'md', null),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                BaseNewarcButton(
                  horizontalPadding: 35,
                  color: Color(0xff499B79),
                  textColor: Colors.white,
                  disableButton: false,
                  buttonText: _match != null && _match[0].toString().contains('app') ? "Torna all'app" : "Vai al login",
                  onPressed: () {
                    if (originRegExp.hasMatch(continueUrl)){
    
                      final match = originRegExp.firstMatch(continueUrl);
                      if (match != null) {
                        if( match[0].toString().contains('website') ) {
                          redirectToWebsite();
                        } else {
                          inAppRedirect();
                        }
                      }
                    } else {
                      Navigator.of(context).pushNamed(LoginPage.route);
                    }
                  }
                ),
              ],
            ),
          ],
        );
        setState(() {
          
        });
      } else {

        // showDialog<void>(
        //   context: context,
        //   barrierDismissible: false, // user must tap button!
        //   builder: (BuildContext context) {
        //     return verificationResponseDialog(
        //         context,
        //         ['assets/close.png', 70, 70],
        //         'Si è verificato un problema',
        //         'Non siamo riusciti a verificare il tuo indirizzo di posta elettronica\n Potrebbe darsi che il link non sia valido o sia scaduto!',
        //         false);
        //   },
        // );

        verificationDisplay = Column(
          children: [
            Image.asset('assets/close.png', height: 45, width: 45,),
            SizedBox(height: 15,),
            NarFormLabelWidget(
              label: 'Si è verificato un problema',
              fontSize: 22,
              textColor: Colors.black,
            ),
            SizedBox(height: 5,),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                NarFormLabelWidget(
                  textAlign: TextAlign.center,
                  label: 'Non siamo riusciti a verificare il tuo indirizzo di posta elettronica\n Potrebbe darsi che il link non sia valido o sia scaduto!',
                  fontSize: 17,
                  fontWeight: '500',
                  letterSpacing: 0.2,
                  textColor: Color(0xff686868),
                ),
              ],
            ),
            
          ],
        );
        
      }
    };

    
    
    if( !hasVerifiyResponse ) verificationDisplay = verification_waiting
        ? Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor))
        : Column(
          children: [
            Column(
              children: [
                NarFormLabelWidget(
                  label: 'Ci sei quasi!',
                  fontSize: 22,
                  textColor: Colors.black,
                ),
                SizedBox(height: 5,),
                NarFormLabelWidget(
                  label: "Clicca sul pulsante qui sotto\nper confermare la tua email.",
                  fontSize: 17,
                  fontWeight: '500',
                  letterSpacing: 0.2,
                  textColor: Color(0xff686868),
                ),
                SizedBox(height: 35,),
                // ui_button(context, 'Conferma email', callback, 'primary', 'md', null),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    BaseNewarcButton(
                      horizontalPadding: 35,
                      color: Theme.of(context).primaryColor,
                      textColor: Colors.white,
                      disableButton: false,
                      buttonText: "Conferma email",
                      onPressed: callback
                    ),
                  ],
                ),
              ],
            )
          ],
        );
  }

  // Widget resetPasswordButton(context) {
  //   dynamic callback = () async {
  //     if (!_resetPasswordformKey.currentState!.validate()) {
  //       return;
  //     }
  //     bool pwdResetSuccess = await processPasswordReset(context);

  //     if (pwdResetSuccess) {
  //       showDialog<void>(
  //         context: context,
  //         barrierDismissible: false, // user must tap button!
  //         builder: (BuildContext context) {
  //           return verificationResponseDialog(
  //               context,
  //               ['assets/check.png', 70, 70],
  //               'Password modificata!',
  //               'Torna alla pagina iniziale ed effettua l\'accesso con le nuove credenziali',
  //               true);
  //         },
  //       );
  //     } else {
  //       showDialog<void>(
  //         context: context,
  //         barrierDismissible: false, // user must tap button!
  //         builder: (BuildContext context) {
  //           return verificationResponseDialog(
  //               context,
  //               ['assets/close.png', 70, 70],
  //               'Si è verificato un problema',
  //               'Non siamo riusciti a modificare la tua password\n Contatta l\'assistenza Newarc se il problema persiste',
  //               false,
  //               showButton: false);
  //         },
  //       );
  //     }
  //   };

  //   return ui_button(
  //       context, 'Modifica password', callback, 'primary', 'md', null);
  // }

  void inAppRedirect() async {
    final Uri launchUri = Uri(
      scheme: !appConfig.isProduction ? 'newarcstaging' : 'newarc',
      path: 'app.newarc.com',
    );
    await launchUrl(launchUri);
  }

  redirectToWebsite() async {
    final Uri launchUri = Uri(
      scheme: 'https',
      host: 'compra.newarc.it',
      path: 'compra',
    );
    await launchUrl(launchUri);
  }

  Widget verificationResponseDialog( BuildContext context, image, title, description, success, {showButton = true}) {
    dynamic callback = () {
      
      if (originRegExp.hasMatch(continueUrl)){
        
        final match = originRegExp.firstMatch(continueUrl);
        if (match != null) {
          if( match[0].toString().contains('website') ) {
            redirectToWebsite();
          } else {
            inAppRedirect();
          }
        }
      } else {
        Navigator.of(context).pushNamed(LoginPage.route);
      }
    };

    return AlertDialog(
        content: Builder(
          builder: (context) {
            return Container(
              width: kIsWeb ? 300 : (MediaQuery.of(context).size.width - 100),
              height: kIsWeb ? 350 : (MediaQuery.of(context).size.width - 100),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    image[0],
                    width: image[1],
                    height: image[1],
                  ),
                  SizedBox(height: 40),
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: success
                          ? Theme.of(context).primaryColor
                          : Color.fromARGB(255, 252, 28, 28),
                      fontWeight: FontWeight.bold,
                      fontSize: 30,
                    ),
                  ),
                  SizedBox(height: 40),
                  Text(
                    description,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Color.fromARGB(255, 73, 73, 73),
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                    ),
                  ),
                  SizedBox(height: 40),
                  showButton
                      ? ui_button(context, originRegExp.hasMatch(continueUrl) ? "Vai all'app" : 'Accedi', callback,
                          success ? 'primary' : 'danger', 'md', null)
                      : Container()
                ],
              ),
            );
          },
        ),
        actions: []);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).unselectedWidgetColor,
      body: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          //bool responsive = constraints.maxWidth < 600;
          return Center(
            child: loading
                ? selectLayout()
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(responseText + mode, style: TextStyle(color: Theme.of(context).primaryColorDark)),
                    ],
                  ),
          );
        },
      ),
    );
  }

  Widget selectLayout() {
    if (mode == 'resetPassword') {
      return _resetPasswordLayout();
    } else {
      return _emailVerificationLayout();
    }
  }

  Widget _emailVerificationLayout() {
    verifyButton(context);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/newarc_classic.png',
          width: 200,
        ),
        const SizedBox(height: 30),
        Text(
            'Clicca "Verifica" per verificare il tuo indirizzo di posta elettronica',
            style: TextStyle(color: Colors.white)),
        SizedBox(height: 20),
        verificationDisplay
      ],
    );
  }

  _togglePasswordVisibility(){
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  Widget _resetPasswordLayout() {
    TextTheme textTheme = TextTheme(
      displayLarge: 
        GoogleFonts.raleway(
          fontSize: 25,
          fontWeight: FontWeight.w700,
          color: const Color(0xFF000000),
          height:1.4,
        ),
      displayMedium: 
        GoogleFonts.raleway(
          fontSize: 23,
          fontWeight: FontWeight.w700,
          color: const Color(0xFF000000),
        ),
      displaySmall: 
        GoogleFonts.raleway(
          fontSize: 17,
          fontWeight: FontWeight.w700,
          color: const Color(0xFF000000),
        ),
      labelLarge:
        GoogleFonts.raleway(
          fontSize: 27,
          fontWeight: FontWeight.w600,
        ),
      labelMedium: 
        GoogleFonts.raleway(
          fontSize: 19,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF000000),
        ),
      labelSmall: 
        GoogleFonts.raleway(
          fontSize: 15,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF000000),
        ),
      titleLarge: 
        GoogleFonts.raleway(
          fontSize: 27,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF000000),
        ),
      titleMedium: 
        GoogleFonts.raleway(
          fontSize: 20,
          fontWeight: FontWeight.w700,
          letterSpacing: 0,
          color: const Color(0xFF000000),
        ),
      titleSmall: 
        GoogleFonts.raleway(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          letterSpacing: 1.2,
          color: const Color(0xFF000000),
        ),
    );
    ElevatedButtonThemeData  elevatedButtonTheme = ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: WidgetStateProperty.all<double>(0),
        backgroundColor: WidgetStateProperty.all<Color>(Color(0xff489B79)),
        maximumSize: WidgetStateProperty.all<Size>(
          const Size(double.infinity, 60),
        ),
        minimumSize: WidgetStateProperty.all<Size>(
          const Size(double.infinity, 60),
        ),
        fixedSize: WidgetStateProperty.all<Size>(
          const Size(double.infinity, 60),
        ),
        textStyle: WidgetStateProperty.all<TextStyle>(
          GoogleFonts.raleway(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            height:0,
          ),
        ),
        padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
          const EdgeInsets.all(0),
        ),
        shape: WidgetStateProperty.all<OutlinedBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
    
    return Form(
      key: _resetPasswordformKey,
      child: 
      Container(
        width: MediaQuery.sizeOf(context).width > 600 ? 600 : MediaQuery.sizeOf(context).width,
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Image.asset(
               originRegExp.hasMatch(continueUrl) ? 'assets/rq-pdf-cover-logo.png' : 'assets/newarc_classic.png',
              width: MediaQuery.sizeOf(context).width > 200 ? 200 : MediaQuery.sizeOf(context).width,
            ),
            const SizedBox(height: 30),
            Text(
              'Impostiamo la password',
              style: textTheme.displayLarge
            ),
            const SizedBox(height: 32),
            Text(
              'Inserisci la nuova password',
              style: textTheme.labelSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextFormField(
              style: textTheme.labelSmall,
              decoration: InputDecoration(
                hintText: 'Nuova Password',
                hintStyle: textTheme.labelSmall!.copyWith(color: Theme.of(context).primaryColorLight),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureText ? Icons.visibility : Icons.visibility_off,
                    color: Theme.of(context).primaryColorLight,
                  ),
                  onPressed: _togglePasswordVisibility,
                ),
                enabledBorder: Theme.of(context).inputDecorationTheme.enabledBorder?.copyWith(
                  borderSide: BorderSide(color: Color(0xffdbdbdb)), // Your desired color
                ),
                focusedBorder: Theme.of(context).inputDecorationTheme.focusedBorder?.copyWith(
                  borderSide: BorderSide(color: Color(0xffdbdbdb)), // Your desired color
                ),
              ),
              obscureText: _obscureText ? true : false,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Inserisci una password valida";
                }
                if (!RegExp(
                        r"^(?=.*[a-z])(?=.*[A-Z])(?=.*[@$!%*?&._#:~+^°§<>-])[A-Za-z\d@$!%*?&._#:~+^°§<>-]{8,}$")
                    .hasMatch(value)) {
                  return 'Minimo 8 caratteri, uno maiuscolo,\nuno minuscolo ed uno speciale';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  newPassword = value;
                });
              },
            ),
            SizedBox(height:15),
            TextFormField(
              style: textTheme.labelSmall,
              decoration: InputDecoration(
                hintText: 'Conferma Password',
                hintStyle: textTheme.labelSmall!.copyWith(color: Theme.of(context).primaryColorLight),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureText ? Icons.visibility : Icons.visibility_off,
                    color: Theme.of(context).primaryColorLight,
                  ),
                  onPressed: _togglePasswordVisibility,
                ),
                enabledBorder: Theme.of(context).inputDecorationTheme.enabledBorder?.copyWith(
                  borderSide: BorderSide(color: Color(0xffdbdbdb)), // Your desired color
                ),
                focusedBorder: Theme.of(context).inputDecorationTheme.focusedBorder?.copyWith(
                  borderSide: BorderSide(color: Color(0xffdbdbdb)), // Your desired color
                ),
              ),
              obscureText: _obscureText ? true : false,
              validator: (value) {
                if (value != newPassword) {
                  return "Le password inserite sono diverse";
                }
                return null;
              },
            ),
            Padding(
              padding: const EdgeInsets.only(top: 32.0),
              child: SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  style: elevatedButtonTheme.style,
                  onPressed: () async {
                    if (!_resetPasswordformKey.currentState!.validate()) {
                      return;
                    }
                    bool pwdResetSuccess = await processPasswordReset(context);
                    if (pwdResetSuccess) {
                      showDialog<void>(
                        context: context,
                        barrierDismissible: false, // user must tap button!
                        builder: (BuildContext context) {
                          return verificationResponseDialog(
                              context,
                              ['assets/check.png', 70, 70],
                              'Password modificata!',
                              'Torna alla pagina iniziale ed effettua l\'accesso con le nuove credenziali',
                              true);
                        },
                      );
                    } else {
                      showDialog<void>(
                        context: context,
                        barrierDismissible: false, // user must tap button!
                        builder: (BuildContext context) {
                          return verificationResponseDialog(
                              context,
                              ['assets/close.png', 70, 70],
                              'Si è verificato un problema',
                              'Non siamo riusciti a modificare la tua password\n Contatta l\'assistenza Newarc se il problema persiste',
                              false,
                              showButton: false);
                        },
                      );
                    }
                  },
                  child: Text('Salva la password', style: textTheme.labelMedium!.copyWith(color: Theme.of(context).unselectedWidgetColor),),
                ),
              ),
            ),
          ],
        ),
      )
      // : Column(
      //   mainAxisAlignment: MainAxisAlignment.center,
      //   children: [
      //     Text('Inserisci la nuova password',
      //         style: TextStyle(color: Colors.white)),
      //     SizedBox(height: 8),
      //     Container(
      //       width: 300,
      //       child: TextFormField(
      //         obscureText: !pwdVisible,
      //         validator: (value) {
      //           if (value == null) {
      //             return "Inserisci una password valida";
      //           }
      //         },
      //         onChanged: ((value) => setState(() {
      //               newPassword = value;
      //             })),
      //         decoration: InputDecoration(
      //           label: Text("Nuova password"),
      //           suffixIcon: IconButton(
      //             icon: pwdVisible
      //                 ? Icon(
      //                     Icons.visibility_rounded,
      //                     color: Theme.of(context).primaryColor,
      //                   )
      //                 : Icon(Icons.visibility_off_rounded, color: Colors.grey),
      //             onPressed: () {
      //               setState(() {
      //                 pwdVisible = !pwdVisible;
      //               });
      //             },
      //           ),
      //         ),
      //       ),
      //     ),
      //     SizedBox(height: 20),
      //     resetPasswordButton(context)
      //   ],
      // ),
    );
  }
}
