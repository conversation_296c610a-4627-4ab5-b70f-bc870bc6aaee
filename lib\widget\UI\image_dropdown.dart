import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';

class NarImageDropdown extends StatefulWidget {
  final List<dynamic> options;
  final TextEditingController? controller;
  final Color? borderColor;
  final Color? iconColor;
  final Color? containerColor;
  final String? hintText;
  final TextStyle? textStyle;

  final Function? onChanged;
  final double? iconSize;
  final Widget? customButton;
  final Offset? offset;
  final double? dropdownWidth;

  // final Function? onFieldTap;
  // final String? hint;
  // final String? disabledHint;
  // final bool? autoFocus;

  // final String? validationType;
  // final String? parametersValidate;
  // final String? label;
  // final Color? labelColor;
  // final int? flex;
  // final double? iconSize;
  // final EdgeInsetsGeometry? buttonPadding;

  const NarImageDropdown({
    Key? key,
    required this.options,
    this.controller,
    this.borderColor = AppColor.borderColor,
    this.iconColor = Colors.black,
    this.containerColor = Colors.white,
    this.onChanged,
    this.hintText,
    this.textStyle,
    this.iconSize,
    this.customButton,
    this.offset,
    this.dropdownWidth,
    // this.flex = 1,
    // this.label = '',
    // this.labelColor = const Color(0xff696969),

    // this.hint,
    // this.disabledHint,

    // this.autoFocus,

    // this.onFieldTap,
    // this.validationType,
    // this.parametersValidate,
    // this.iconSize = 24,
    // this.buttonPadding =
    // const EdgeInsets.only(top: 17, bottom: 17, left: 21.0, right: 8.0)
  }) : super(key: key);

  @override
  State<NarImageDropdown> createState() => _NarColorBgDropdownState();
}

class _NarColorBgDropdownState extends State<NarImageDropdown> {
  double bottomPaddingToError = 12;
  Map? dropdownValue;
  String? selectedValue;

  @override
  void initState() {
    super.initState();

    widget.options.removeWhere((test) => test['value'] == '' && test['label'] == '');
    if (widget.controller!.text == '') {
      dropdownValue = widget.options.first;
    } else {
      try {
        dropdownValue = widget.options.where((element) => element['value'] == widget.controller!.text).toList().first;
      } catch (e) {
        dropdownValue = widget.options.first;
      }
    }
  }

  @override
  void didUpdateWidget(covariant NarImageDropdown oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);

    if (widget.controller!.text == '') {
      dropdownValue = widget.options.first;
    } else {
      try {
        dropdownValue = widget.options.where((element) => element['value'] == widget.controller!.text).toList().first;
      } catch (e) {
        dropdownValue = widget.options.first;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: widget.borderColor ?? AppColor.borderColor,
          ),
          color: widget.containerColor ?? AppColor.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton2(
            isExpanded: true,
            style: TextStyle(
              fontSize: 11,
              fontFamily: 'Raleway-600',
              color: AppColor.black,
            ),
            value: dropdownValue,
            customButton: widget.customButton,
            enableFeedback: true,
            hint: NarFormLabelWidget(
              label: widget.hintText ?? "",
              fontSize: 11,
              fontWeight: '600',
              textColor: AppColor.greyColor,
            ),
            dropdownStyleData: DropdownStyleData(
              width: widget.dropdownWidth,
              offset: widget.offset ?? Offset.zero,
              elevation: 0,
              padding: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: AppColor.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1,
                  color: AppColor.borderColor,
                ),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 7),
                    color: AppColor.black.withOpacity(.08),
                    blurRadius: 17,
                  )
                ],
              ),
            ),
            buttonStyleData: ButtonStyleData(
              decoration: BoxDecoration(
                color: AppColor.white,
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
              padding: EdgeInsets.symmetric(horizontal: 10),
              height: 30,
              width: 148,
            ),
            menuItemStyleData: MenuItemStyleData(
              height: 40,
            ),
            iconStyleData: IconStyleData(
              iconSize: 15,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
              ),
            ),
            items: widget.options
                .map(
                  (item) => DropdownMenuItem<Map>(
                value: item,
                child: Container(
                  child: Row(
                    children: [
                      if( item['image'] != null ) Container(
                        margin: EdgeInsets.only(right: 5),
                        child: getImageWidget(item['image'],iconColor: item['noIconColor'] ?? false ? null : item['iconColor'].runtimeType.toString() != 'Null' ? item['iconColor'] : Colors.black,iconSize: widget.iconSize ?? 15),
                      ),
                      NarFormLabelWidget(
                        label: item['label'],
                        textColor:  item['labelColor'].runtimeType.toString() != 'Null' ? item['labelColor'] : Colors.black,
                        fontSize: 12,
                        // fontWeight: '600',
                      )
                    ],
                  ),
                ),
              ),
            )
                .toList(),
            onChanged: (Map? value) {
              // value as MenuItem;
              setState(() {
                widget.controller!.text = value!['value'];
                dropdownValue = value;
              });

              if (widget.onChanged != null) {
                widget.onChanged!(value);
              }
            },
          ),
        ),
      );
    } catch (e) {
      return Text('');
    }
  }

  Widget getImageWidget(String imagePath, {Color? iconColor, double iconSize = 20}) {
    if (imagePath.endsWith('.svg')) {
      return SizedBox(
        height: iconSize,
        width: iconSize,
        child: SvgPicture.asset(
          imagePath,
          fit: BoxFit.contain, // Ensures the SVG fits properly without cropping
          color: iconColor != null
              ? iconColor
              : null,
        ),
      );
    } else if (imagePath.startsWith('http')) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(iconSize / 2),
        clipBehavior: Clip.hardEdge, // Prevents overflow
        child: Image.network(
          imagePath,
          height: iconSize,
          width: iconSize,
          fit: BoxFit.scaleDown, // Prevents cropping while keeping proportions
        ),
      );
    } else {
      return Image.asset(
        imagePath,
        height: iconSize,
        width: iconSize,
        color: iconColor != null
            ? iconColor
            : null,
        fit: BoxFit.scaleDown,
      );
    }
  }
}
