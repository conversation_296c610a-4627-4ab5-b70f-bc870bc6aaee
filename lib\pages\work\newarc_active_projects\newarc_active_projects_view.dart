import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/newarc_active_projects/newarc_active_projects_controller.dart';
import 'package:newarc_platform/pages/work/newarc_active_projects/newarc_active_projects_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/utils/const.dart' as utilConst;
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/tab/users_stack_list.dart';
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class NewarcActiveProjectsView extends StatefulWidget {
  final bool isArchived;

  // final AgencyUser agencyUser;
  final NewarcUser newarcUser;
  final Function? updateViewCallback;
  final Map? projectArguments;
  final bool forceDataFetch;

  static const String route = '/property/index';

  const NewarcActiveProjectsView(
      {super.key,
      required this.isArchived,
      required this.newarcUser,
      // required this.agencyUser,
      this.updateViewCallback,
      this.forceDataFetch = false,
      this.projectArguments = const {}});

  @override
  State<NewarcActiveProjectsView> createState() => _NewarcActiveProjectsViewState();
}

class _NewarcActiveProjectsViewState extends State<NewarcActiveProjectsView> {
  final controller = Get.put<NewarcActiveProjectsViewController>(NewarcActiveProjectsViewController());

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  bool isRestorationProject = false;
  List allowedRenoContactIds = [];

  @override
  void initState() {
    super.initState();
    controller.filters.clear();
    controller.contSearchProjectType = '';
    controller.contSearchProjectTypeController.clear();

    if (controller.currentMenu != widget.projectArguments?['current_menu']) {
      controller.projects = [];
      controller.currentMenu = widget.projectArguments?['current_menu'];
    }
    isRestorationProject = widget.projectArguments?['isRestoration'];

    if( isRestorationProject ) {
      controller.contProjectType.text = "Ristrutturazione";
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      
      initialFetch(force: widget.forceDataFetch);
      fetchRenovationContacts();
      fetchEconomic();

      
    });
  }

  

  fetchEconomic() async {

    QuerySnapshot<Map<String, dynamic>> collectionEconomicSnapshot =
        await FirebaseFirestore.instance.collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT).where('isArchived', isEqualTo: false).orderBy('created', descending: true).get();
    if (collectionEconomicSnapshot.docs.length > 0) {
      controller.projectEconomicData.add({'label': '', 'value': ''});
      for (var i = 0; i < collectionEconomicSnapshot.docs.length; i++) {
        ProjectEconomic tmp = ProjectEconomic.fromDocument(collectionEconomicSnapshot.docs[i].data(), collectionEconomicSnapshot.docs[i].id);

        controller.projectEconomic.add(tmp);
        String address = tmp.addressInfo != null ? "${tmp.addressInfo!.toShortAddress()}" : tmp.address! + ', ' + tmp.city!;
        controller.projectEconomicData.add({'label': address, 'value': tmp.firebaseId});
      }
    }
  }

  fetchRenovationContacts() async {
    if (controller.contacts.length > 0) {
      controller.loadingContacts.value = false;
      return;
    }

    setState(() {
      controller.loadingContacts.value = true;
    });

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_QUOTATION);
    collectionSnapshot = await collectionSnapshotQuery
    .where('status', isEqualTo: 'accettato')
    .where('assignedRenovationProjectId', isNull: true )
    .get();

    controller.contacts.add({'value': '', 'label': ''});

    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          var _tmp = RenovationQuotation.fromDocument(element.data(), element.id);
          
          if( _tmp.renovationContactAddressId != null ) {
            DocumentSnapshot<Map<String, dynamic>> renoContactAddressSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
            .doc( _tmp.renovationContactAddressId )
            .get();
            if( renoContactAddressSnapshot.data() != null ) {
              _tmp.renovationContactAddress = RenovationContactAddress.fromDocument(renoContactAddressSnapshot.data()!, renoContactAddressSnapshot.id);
            }
          }

          if( _tmp.renovationContactId != null ) {
            DocumentSnapshot<Map<String, dynamic>> renoContactSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
            .doc( _tmp.renovationContactId )
            .get();
            if( renoContactSnapshot.data() != null ) {
              _tmp.renovationContact = RenovationContact.fromDocument(renoContactSnapshot.data()!, renoContactSnapshot.id);
            }
          }
          
          String _code = "${_tmp.code ?? ""}_V${_tmp.version ?? ""}_R${_tmp.revision ?? ""}";
          Map _tmpContact = {'value': _tmp.id, 'label': _code + ' - ' + _tmp.renovationContactAddress!.addressInfo!.toShortAddress() };
          controller.renovationContact.add(_tmp);
          controller.contacts.add(_tmpContact);

        } catch (e) {
          print(e);
        }
      }
    }

    setState(() {
      controller.loadingContacts.value = false;
    });
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties.value == true) return;
                    fetchPrevProperties();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties.value == true) return;

                    fetchNextProperties();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }

  _initialFetch({bool force = false}) {
    initialFetch(force: force);
  }

  Future<void> initialFetch({bool force = false}) async {

    utilConst.projectJobsActivities = await getProjectActivities();

    if (controller.projects.isNotEmpty && !force) return;

    controller.pageCounter = 1;

    setState(() {
      controller.projects = [];
      controller.loadingProperties.value = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> counterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      if (controller.contSearchProjectTypeController.text != '') {
        dataQuery = FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARC_PROJECTS)
            .where('isArchived', isEqualTo: widget.isArchived)
            .where('type', isEqualTo: controller.contSearchProjectTypeController.text.trim())
            .orderBy('created', descending: true);

        counterQuery = FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARC_PROJECTS)
            .where('isArchived', isEqualTo: widget.isArchived)
            .where('type', isEqualTo: controller.contSearchProjectTypeController.text.trim())
            .orderBy('created', descending: true);
      } else {

        dataQuery = FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .where('isArchived', isEqualTo: widget.isArchived)
        .orderBy('created', descending: true);
        
        if( isRestorationProject ) {
          
          dataQuery = dataQuery.where('type', isEqualTo: 'Ristrutturazione');

        } else {
          dataQuery = dataQuery.where('type', isNotEqualTo: 'Ristrutturazione');
        }

        dataQuery.orderBy('created', descending: true);


        counterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).where('isArchived', isEqualTo: widget.isArchived);

        if( isRestorationProject ) {
          counterQuery = counterQuery.where('type', isEqualTo: 'Ristrutturazione');
        } else {
            counterQuery = counterQuery.where('type', isNotEqualTo: 'Ristrutturazione');
        }
        counterQuery.orderBy('created', descending: true);
        
      }

      collectionSnapshot = await dataQuery.limit(controller.recordsPerPage).get();

      collectionSnapshotCounter = await counterQuery.get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;
      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
      if (mounted) {
        setState(() {});
      }
    } catch (e, s) {
      print({e, s});
      setState(() {
        controller.loadingProperties.value = false;
      });
    }
  }

  fetchNextProperties() async {
    setState(() {
      controller.loadingProperties.value = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).where('isArchived', isEqualTo: widget.isArchived);

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        if( isRestorationProject ) {
          dataQuery.where('type', isEqualTo: 'Ristrutturazione');
          
        }
        

        collectionSnapshot =
            await dataQuery.orderBy('created', descending: true).limit(controller.recordsPerPage).startAfterDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties.value = false;
      });
    }
  }

  fetchPrevProperties() async {
    setState(() {
      controller.loadingProperties.value = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).where('isArchived', isEqualTo: widget.isArchived);

        if (controller.filters.isNotEmpty) {
          for (var i = 0; i < controller.filters.length; i++) {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
        
        if( isRestorationProject ) {
          dataQuery.where('type', isEqualTo: 'Ristrutturazione');
        }

        collectionSnapshot = await dataQuery.orderBy('created', descending: true).limit(controller.recordsPerPage).endBeforeDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties.value = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }

    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    
    List<NewarcProject> _projects = [];
    for (var element in collectionSnapshot.docs) {
      NewarcProject _tmp = NewarcProject.fromDocument(element.data(), element.id);

      if( widget.newarcUser.isFilterPerAccountEnabled! ) {
        
        int userIndex = _tmp.assignedTeam.indexWhere((e) => e['userId'] == widget.newarcUser.id );
        if( userIndex > -1 ) {
          _projects.add(_tmp);
        }
      } else {
        _projects.add(_tmp);
      }

      
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_projects.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_projects.length > 0 && _projects.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _projects.length).toString();
    }

    for (var i = 0; i < _projects.length; i++) {
      int projectIndex = controller.projects.indexWhere((e) => e.id == _projects[i].id );
      if( projectIndex == -1 ) {
        controller.projects.add(_projects[i]);
      } 
    }
    
    // controller.projects = _projects;
    controller.loadingProperties.value = false;
    if (mounted) {
      setState(() {});
    }
    
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      key: scaffoldKey,
      drawer: CustomDrawer(),
      body: LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: isRestorationProject 
                  ? (widget.isArchived ? 'Storico ristrutturazioni' : 'Ristrutturazioni in corso')
                  : (widget.isArchived ? 'Storico progetti' : 'Progetti in corso'),
                  fontSize: 19,
                  fontWeight: '700',
                ),
                _create(context),
              ],
            ),

            // Filter goes here
            _filter(),

            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                          opacity: controller.loadingProperties.value ? 0.5 : 1,
                          child: NewarcDataTable(
                            rowsPerPage: 20,
                            isHasDecoration: false,
                            hidePaginator: true,
                            onPageChanged: (val) {
                              
                            },
                            source: ProjectRowSource(
                              projects: controller.projects,
                              isRestorationProject: isRestorationProject,
                              context: context,
                              onNameTap: (String id) {
                                widget.projectArguments!.clear();
                                widget.projectArguments!.addAll({
                                  'projectFirebaseId': id,
                                  'property': null,
                                  'updateViewCallback': widget.updateViewCallback,
                                  'initialFetchProperties': _initialFetch(force: true),
                                  isRestorationProject: isRestorationProject,
                                });

                                widget.updateViewCallback!('progetti-in-corso-single', projectArguments: widget.projectArguments);
                              },
                            ),
                            columns: getColumns1(constraints),
                          ),
                        ),
                        if (controller.loadingProperties.value)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            )

            // dataTablePagination(),
          ],
        );
      }),
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      isFilterHide: true,
      searchHintText: "Cerca per indirizzo...",
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<NewarcProject> filtered = controller.projects.where((project) {
              final address = project.addressInfo;
              final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
              final streetName = address?.streetName?.toLowerCase() ?? '';
              final fullAddress = address?.fullAddress?.toLowerCase() ?? "";
              return
                  city.contains(searchQuery.toLowerCase()) ||
                  streetName.contains(searchQuery.toLowerCase()) ||
                  fullAddress.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.projects = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        //await initialFetch(force: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<NewarcProject> filtered = controller.projects.where((project) {
            final address = project.addressInfo;
            final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? "";
            return city.contains(controller.searchTextController.text.toLowerCase()) ||
                streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.projects = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      searchTextEditingControllers: controller.searchTextController,
      selectedFilters: [controller.contSearchProjectType],
      textEditingControllers: [controller.contSearchProjectTypeController],
      filterFields: [
        {
          'Tipologia': NarSelectBoxWidget(
            options: isRestorationProject
              ? [ "Ristrutturazione" ]
              : ["Newarc Subito", "Newarc Insieme"],
            onChanged: (value) {
              controller.contSearchProjectType = controller.contSearchProjectTypeController.text;

              setState(() {});
            },
            controller: controller.contSearchProjectTypeController,
          ),
        },
      ],
      onSubmit: () async {
        controller.filters = [
          {
            'field': 'type',
            'value': controller.contSearchProjectType,
          }
        ];
        await initialFetch(force: true);
      },
      onReset: () async {
        controller.filters.clear();
        controller.contSearchProjectType = '';
        controller.contSearchProjectTypeController.clear();
        await initialFetch(force: true);
      },
    );
  }

  MouseRegion _create(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          controller.formMessages.clear();
          
          if( isRestorationProject ) {
            controller.contProjectType.text = "Ristrutturazione";
          } else {
            controller.contProjectType.text = '';
          }

          controller.contProjectEconomic.text = '';
          controller.contRenovationContact.text = '';
          controller.contProjectName.text = '';

          return await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                  return Center(
                    child: BaseNewarcPopup(
                      formErrorMessage: controller.formMessages,
                      buttonText: 'Crea progetto',
                      disableButton: controller.contProjectType.text != 'Ristrutturazione' && controller.projectEconomic.length == 0 ? true : false,
                      onPressed: () async {
                        setState(() {
                          controller.formMessages.clear();
                          controller.formMessages.add('Creazione in corso..');
                        });
                        // initial input checks depending on newarc project type
                        if (controller.contProjectType.text != 'Ristrutturazione' && controller.contProjectEconomic.text == '') {
                          controller.formMessages.clear();
                          controller.formMessages.add('Impossibile creare progetto.');
                          return false;
                        }
                        if (controller.contProjectType.text == 'Ristrutturazione' && controller.contRenovationContact.text == '') {
                          controller.formMessages.clear();
                          controller.formMessages.add('Impossibile creare progetto.');
                          return false;
                        }
                        // db instance
                        final FirebaseFirestore _db = FirebaseFirestore.instance;
                        // create new project data
                        Map<String, dynamic> projectData = {
                          'name': controller.contProjectEconomic.text,
                          'city': '',
                          'type': controller.contProjectType.text,
                          'fixedProperty': {},
                          'renovationContactId': controller.contRenovationContact.text,
                          'created': Timestamp.now().millisecondsSinceEpoch,
                          'provisionalAccountId': controller.contProjectEconomic.text,
                          'isArchived': false,
                          'propertyId': null,
                          'addressInfo': null,
                        };
                        // update project data based on project type
                        if (controller.contProjectType.text != 'Ristrutturazione') {
                          ProjectEconomic tmpEco = controller.projectEconomic.firstWhere((element) => element.firebaseId == controller.contProjectEconomic.text);
                          final address = tmpEco.addressInfo != null ? "${tmpEco.addressInfo!.toShortAddress()}" : tmpEco.address;
                          final city = tmpEco.addressInfo != null ? tmpEco.addressInfo!.city : tmpEco.city;
                          final addressInfo = tmpEco.addressInfo != null ? tmpEco.addressInfo : null;
                          projectData['name'] = address;
                          projectData['city'] = city;
                          projectData['addressInfo'] = addressInfo?.toMap();
                          projectData['fixedProperty'] = {'propertyName': address, 'city': city};
                        } else {
                          
                          RenovationQuotation tmpReno = controller.renovationContact.firstWhere((element) => element.id == controller.contRenovationContact.text);
                          
                          final address = tmpReno.renovationContactAddress!.addressInfo != null ? "${tmpReno.renovationContactAddress!.addressInfo!.toShortAddress()}" : '';

                          final city = tmpReno.renovationContactAddress!.addressInfo != null ? tmpReno.renovationContactAddress!.addressInfo!.city : '';
                          
                          final addressInfo = tmpReno.renovationContactAddress!.addressInfo != null ? tmpReno.renovationContactAddress!.addressInfo : null;
                          projectData['name'] = address;
                          projectData['city'] = city;
                          projectData['addressInfo'] = tmpReno.renovationContactAddress!.addressInfo!.toMap();
                          projectData['fixedProperty'] = {
                            'propertyName': address, 
                            'city': city,
                            'propertyType': tmpReno.propertyType,
                            'areaMQ': double.tryParse(tmpReno.areaMq.toString()),
                            'floors': tmpReno.floor,
                            'baths': int.tryParse(tmpReno.bathroom.toString()) ,
                          };

                          projectData['renovationContactId'] = tmpReno.renovationContactId;
                        }
                        // attempt firebase user creation if project is Ristrutturazione
                        if (controller.contProjectType.text == 'Ristrutturazione' ) try { 
                          {
                            RenovationQuotation tmpReno = controller.renovationContact.firstWhere((element) => element.id == controller.contRenovationContact.text);
                            await FirebaseFunctions.instance
                                      .httpsCallable('createUser')
                                      .call({
                                'email': tmpReno.renovationContact!.personInfo!.email,
                                'password': 'cambiami!',
                              });
                          }
                        } on FirebaseFunctionsException catch (e) {
                          
                          if (e.details['originalError']['code'] == 'auth/invalid-email') {
                            setState(() {
                              controller.formMessages.clear();
                              controller.formMessages.add("L'email inserita non è valida");
                            });
                          }
                          
                          /*We don't need to show error if the user has a project already*/
                          if( e.details['originalError']['code'] == 'auth/email-already-exists' ) {
                            // return true;
                          } else {
                            return false;
                          }
                          // return false;
                        } catch (e, s) {
                          setState(() {
                            controller.formMessages.clear();
                            controller.formMessages.add("Errore generico nella creazione account");
                          });
                          print({e, s});
                          return false;
                        }

                        // create newarcProject object
                        try {
                          DocumentReference<Map<String, dynamic>> projectResponse = await _db.collection(appConfig.COLLECT_NEWARC_PROJECTS).add(projectData);
                          // update renovation contact if project is Ristrutturazione
                          if (controller.contProjectType.text == 'Ristrutturazione') {
                            
                            RenovationQuotation tmpReno = controller.renovationContact.firstWhere((element) => element.id == controller.contRenovationContact.text);

                            tmpReno.assignedRenovationProjectId = projectResponse.id;

                            await _db.collection(appConfig.COLLECT_RENOVATION_QUOTATION).doc(controller.contRenovationContact.text).update(tmpReno.toMap());
                          }
                          // update projectEconomic if project is Insieme or Subito
                          if (controller.contProjectType.text != 'Ristrutturazione') {
                            ProjectEconomic selectedPE = controller.projectEconomic.where((element) => element.firebaseId == controller.contProjectEconomic.text).first;
                            selectedPE.isAssigned = true;
                            await _db.collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT).doc(selectedPE.firebaseId).update(selectedPE.toMap());
                          }

                          setState(() {
                            controller.formMessages.clear();
                            controller.formMessages.add('Project created!');
                          });

                          // redirect to internal project view
                          if (projectResponse.id != '') {
                            Map projectArgs = {
                              'projectFirebaseId': projectResponse.id,
                              // 'agencyUser': widget.agencyUser,
                              'updateViewCallback': widget.updateViewCallback,
                              'initialFetchProperties': _initialFetch(force: true)
                            };
                            widget.updateViewCallback!('progetti-in-corso-single', projectArguments: projectArgs);
                            return true;
                          }
                        } catch (e,s) {
                          print({e,s});
                          return false;
                        }
                        

                        
                      },
                      title: "Crea nuovo progetto",
                      column: Container(
                        width: 600,
                        margin: EdgeInsets.symmetric(vertical: 30),
                        child: Center(
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Visibility(
                                    visible: !isRestorationProject,

                                    child: Expanded(
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: "Tipologia",
                                            textColor: Color(0xff696969),
                                            fontSize: 14,
                                            fontWeight: '600',
                                          ),
                                          SizedBox(height: 4),
                                          NarSelectBoxWidget(
                                              options: isRestorationProject
                                              ? [ "Ristrutturazione" ]
                                              : ["Newarc Subito", "Newarc Insieme"],
                                              onChanged: (value) {
                                                if (controller.contProjectType.text == "Ristrutturazione") {
                                                  controller.titleEnabled = false;
                                                } else {
                                                  controller.titleEnabled = true;
                                                }
                                                setState(() {});
                                              },
                                              controller: controller.contProjectType,
                                              validationType: 'required',
                                              parametersValidate: 'Obbligatorio!'),
                                        ],
                                      ),
                                    ),
                                  )
                                  
                                ],
                              ),
                              controller.loadingContacts == false && controller.contProjectType.text == 'Ristrutturazione'
                                  ? Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.max,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                children: [
                                                  SizedBox(height: 18),
                                                  NarFormLabelWidget(
                                                    label: "Assegna cliente",
                                                    textColor: Color(0xff696969),
                                                    fontSize: 14,
                                                    fontWeight: '600',
                                                  ),
                                                  SizedBox(height: 4),
                                                  NarImageSelectBoxWidget(
                                                    options: controller.contacts,
                                                    controller: controller.contRenovationContact,
                                                    validationType: 'required',
                                                    parametersValidate: 'Obbligatorio!',
                                                    onChanged: (val) {
                                                      RenovationQuotation tmp = controller.renovationContact.where((element) => element.id == controller.contRenovationContact.text).first;
                                                      
                                                      controller.contProjectName.text = tmp.renovationContactAddress!.addressInfo!.toShortAddress();
                                                      
                                                      setState(() {});

                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 12),
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            CustomTextFormField(
                                              label: "Nome progetto",
                                              controller: controller.contProjectName,
                                              enabled: controller.titleEnabled,

                                              // validationMessage: 'Required!',
                                              validator: (value) {
                                                if (value == '') {
                                                  return 'Obbligatorio!';
                                                }

                                                return null;
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    )
                                  : SizedBox(height: 0),
                              controller.loadingContacts == false &&
                                      controller.contProjectType.text != '' &&
                                      controller.contProjectType.text != 'Ristrutturazione'
                                  ? Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: controller.projectEconomicData.length == 0
                                              ? Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(height: 18),
                                                    NarFormLabelWidget(label: 'Nessun conto economico previsionale disponibile'),
                                                  ],
                                                )
                                              : Column(
                                                  mainAxisSize: MainAxisSize.max,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: [
                                                    SizedBox(height: 18),
                                                    NarFormLabelWidget(
                                                      label: "Collega conto economico previsionale",
                                                      textColor: Color(0xff696969),
                                                      fontSize: 14,
                                                      fontWeight: '600',
                                                    ),
                                                    SizedBox(height: 4),
                                                    NarImageSelectBoxWidget(
                                                      options: controller.projectEconomicData,
                                                      controller: controller.contProjectEconomic,
                                                      validationType: 'required',
                                                      parametersValidate: 'Obbligatorio!',
                                                      onChanged: (val) {
                                                        
                                                        setState(() {});
                                                      },
                                                    ),
                                                  ],
                                                ),
                                        ),
                                      ],
                                    )
                                  : SizedBox(height: 0),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                });
              });
        },
        child: Container(
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text(
              "Crea progetto",
              style: TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<DataColumn> getColumns1(BoxConstraints constraints) {
    List<DataColumn> list = [];

    list.add(
      DataColumn2(
        label: Text(
          'Progetto',
        ),
      ),
    );

    list.add(
      DataColumn2(
        label: Text(
          'Tipologia',
        ),
      ),
    );

    list.add(
      DataColumn2(
        label: Text(
          'Persone',
        ),
      ),
    );

    list.add(
      DataColumn2(
        label: Text(
          'Avanzamento',
        ),
      ),
    );

    if( !isRestorationProject ) list.add(
      DataColumn2(
        label: Text(
          'Agenzia',
        ),
      ),
    );

    list.add(
      DataColumn2(
        label: Text(
          'Inizio lavori',
        ),
      ),
    );

    return list;
  }
}

Future<String> getImageUrl(String userId) async {
  final extensions = ['.jpeg', '.png', '.jpg'];
  for (final extension in extensions) {
    final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
    try {
      return await ref.getDownloadURL();
    } catch (error) {
      continue;
    }
  }
  
  return await printUrl('utils/', "", "imageplaceholder.png");
}

class ProjectRowSource extends DataTableSource {
  ProjectRowSource({required this.projects, this.isRestorationProject = false, required this.onNameTap, required this.context});

  List<NewarcProject> projects = [];
  bool isRestorationProject;
  BuildContext context;
  Function(String) onNameTap;

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final row = projects[index];
      double completedPercentage = 0;

      if ((row.projectJobs?.length ?? 0) > 0) {
        completedPercentage = calculateProjectCompletionPercentage(row.projectJobs);
      }

      String _date = '';
      if (row.jobStartDate! > 0) {
        DateTime dateOn = DateTime.fromMillisecondsSinceEpoch(row.jobStartDate!);
        _date =
            (dateOn.day > 9 ? dateOn.day.toString() : '0' + dateOn.day.toString()) + '/' + (dateOn.month > 9 ? dateOn.month.toString() : '0' + dateOn.month.toString()) + '/' + dateOn.year.toString();
      }
      String address = row.name ?? "";
      if (row.addressInfo != null) {
        address = row.addressInfo!.toShortAddress();
      }
      return DataRow(
        cells: [
          DataCell(
            NarLinkWidget(
              text: address,
              textColor: Colors.black,
              fontWeight: '700',
              fontSize: 12,
              onClick: () {
                onNameTap(row.id ?? "0");
              },
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.type ?? "",
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            FutureBuilder<List<String>>(
              future: Future.wait(
                row.assignedTeam.where((user) => user['userId'] != "").map((user) => getImageUrl(user['userId'])).toList(),
              ),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Container(
                    height: 30,
                    width: 150,
                    alignment: Alignment.centerLeft,
                    child: UsersStackWidget(
                      imageList: snapshot.data ?? [],
                      radius: 25,
                      itemCount: 7,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: Colors.grey,
                    ),
                  );
                }
                return SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 1,
                  ),
                );
              },
            ),
          ),
          DataCell(
          Stack(
            children: [
              Container(
                height: 10,
                width: 150,
                decoration: BoxDecoration(color: Color(0xffd9d9d9), borderRadius: BorderRadius.circular(100)),
              ),
              Container(
                height: 10,
                width: 150 * completedPercentage,
                decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(100)),
              ),
            ],
          )),
          if( !isRestorationProject ) DataCell(
            NarFormLabelWidget(
                label: row.assignedAgency!['agencyName'] == null ? 'Da assegnare' : row.assignedAgency!['agencyName'],
                fontSize: 12,
                fontWeight: '600',
                overflow: TextOverflow.ellipsis,
                textColor: row.assignedAgency!['agencyName'] == null ? Color(0xff969696) : AppColor.black),
          ),
          DataCell(
            NarFormLabelWidget(label: _date, fontSize: 12, fontWeight: '600', overflow: TextOverflow.ellipsis, textColor: AppColor.black),
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
