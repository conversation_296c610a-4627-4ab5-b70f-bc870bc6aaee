import 'package:newarc_platform/classes/renovationQuotation.dart';

class CMEInformation{
  String? uniqueId;
  String? projectDescription;
  String? limitedTrafficZone; // ZTL
  String? blueStripes; // Striscie blu
  String? drivewayAccess; // Accesso carrabile
  String? possibilityOfFreightElevator; // Possibilità montacarichi
  String? possibilityOfRubbleContainer; // Possibilità cassone macerie
  String? elevator; // Ascensore
  double? walkableSquareMeters; // Metri quadri calpestabili
  double? roomHeight; // Altezza dei locali
  List<ExecutiveGraphicDrawing>? executiveGraphicDrawingList;
  String? specificationDescription;
  String? virtualTourURL;


  CMEInformation.empty(){
    this.uniqueId = "";
    this.projectDescription = "";
    this.limitedTrafficZone = "";
    this.blueStripes = "";
    this.drivewayAccess = "";
    this.possibilityOfFreightElevator = "";
    this.possibilityOfRubbleContainer = "";
    this.elevator = "";
    this.walkableSquareMeters = null;
    this.roomHeight = null;
    this.executiveGraphicDrawingList = [];
    this.specificationDescription = "";
    this.virtualTourURL = "";
  }

  Map<String, dynamic> toMap() {
    return {
      'uniqueId': this.uniqueId,
      'projectDescription': this.projectDescription,
      'limitedTrafficZone': this.limitedTrafficZone,
      'blueStripes': this.blueStripes,
      'drivewayAccess': this.drivewayAccess,
      'possibilityOfFreightElevator': this.possibilityOfFreightElevator,
      'possibilityOfRubbleContainer': this.possibilityOfRubbleContainer,
      'elevator': this.elevator,
      'walkableSquareMeters': this.walkableSquareMeters,
      'roomHeight': this.roomHeight,
      'executiveGraphicDrawingList': this.executiveGraphicDrawingList?.map((e) => e.toMap()).toList(),
      'specificationDescription': this.specificationDescription,
      'virtualTourURL': this.virtualTourURL,
    };
  }

  CMEInformation.fromDocument(Map<String, dynamic> data) {
    try {
      this.uniqueId = data["uniqueId"] ?? "";
      this.projectDescription = data["projectDescription"] ?? "";
      this.limitedTrafficZone = data["limitedTrafficZone"] ?? "";
      this.blueStripes = data["blueStripes"] ?? "";
      this.drivewayAccess = data["drivewayAccess"] ?? "";
      this.possibilityOfRubbleContainer = data["possibilityOfRubbleContainer"] ?? "";
      this.possibilityOfFreightElevator = data["possibilityOfFreightElevator"] ?? "";
      this.elevator = data["elevator"] ?? "";
      this.walkableSquareMeters = data["walkableSquareMeters"];
      this.roomHeight = data["roomHeight"];
      this.executiveGraphicDrawingList = [];
      if(data["executiveGraphicDrawingList"] != null){
        for (var i = 0; i < data['executiveGraphicDrawingList'].length; i++) {
          this.executiveGraphicDrawingList?.add(ExecutiveGraphicDrawing.fromDocument(data['executiveGraphicDrawingList'][i]));
        }
      }
      this.specificationDescription = data["specificationDescription"] ?? "";
      this.virtualTourURL = data["virtualTourURL"] ?? "";
    } catch (e, s) {
      print({'CMEInformation.dart', e, s});
    }

  }

}

class ExecutiveGraphicDrawing {
  String? type;
  // String? fileName;
  // String? location;
  List<Map<String,dynamic>>? filePath;
  List? imageList;


  ExecutiveGraphicDrawing.empty(){
    this.type = "";
    this.filePath = [];
    this.imageList = [];
  }

  Map<String, dynamic> toMap() {
    return {
      'type': this.type,
      'filePath': this.filePath?.map((file)=>file).toList(),
    };
  }

  ExecutiveGraphicDrawing.fromDocument(Map<String, dynamic> data) {

    try {
      this.type = data['type'];
      this.filePath = [];
      if(data['filePath'] != null){
        for(int i = 0;i<data['filePath'].length;i++){
          if (data['filePath'] != null && data['filePath'] is List) {
            this.filePath = List<Map<String, dynamic>>.from(data['filePath']);
          }
        }
        this.imageList = this.filePath!.map((image) => image["fileName"]).where((filename) => filename.isNotEmpty).toList();
      }else{
        this.imageList = [];
      }
    } catch (e, s) {
      print({'CMEInformation.dart', e, s});
    }

  }
}