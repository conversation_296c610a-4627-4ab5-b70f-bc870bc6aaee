import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/projectJob.dart';

import 'comparabile.dart';

class ImmaginaProject {
  int? receivedSuccessFeePaymentDate;

  bool? isPaidWithCredits;
  String? appliedSuccessFee;

  bool? isArchived;
  String? propertyId;

  bool? isAgencyArchived;
  bool? isWorkArchived;
  int? statusChangedDate;
  int? projectCompletedDate;
  String? housingUnit;
  

  late String id;
  late int insertionTimestamp;
  late String agencyId;
  late String projectId;
  String? professionalId;

  bool? isThroughAgency;
  SuggestedAgency? suggestedAgency;

  // Localizzazione
  @Deprecated('Use ImmaginaProject.addressInfo.streetName instead, keep in mind retrocompatibility')
  String? streetName;
  @Deprecated('Use ImmaginaProject.addressInfo.streetNumber instead, keep in mind retrocompatibility')
  String? streetNumber;
  @Deprecated('Use ImmaginaProject.addressInfo.city instead, keep in mind retrocompatibility')
  String? city;
  @Deprecated('Use ImmaginaProject.addressInfo.marketZone instead, keep in mind retrocompatibility')
  String? marketZone;
  @Deprecated('Use ImmaginaProject.addressInfo.province instead, keep in mind retrocompatibility')
  String? province;
  @Deprecated('Use ImmaginaProject.addressInfo.region instead, keep in mind retrocompatibility')
  String? region;
  @Deprecated('Use ImmaginaProject.addressInfo.country instead, keep in mind retrocompatibility')
  String? country;
  @Deprecated('Use ImmaginaProject.addressInfo.postalCode instead, keep in mind retrocompatibility')
  String? postalCode;
  @Deprecated('Use ImmaginaProject.addressInfo.fullAddress instead, keep in mind retrocompatibility')
  String? fullAddress;
  @Deprecated('Use ImmaginaProject.addressInfo.latitude instead, keep in mind retrocompatibility')
  double? latitude;
  @Deprecated('Use ImmaginaProject.addressInfo.longitude instead, keep in mind retrocompatibility')
  double? longitude;
  BaseAddressInfo? addressInfo;

  // Info Generali
  int? grossSquareFootage;
  int? rooms;
  int? numberOfBathrooms;
  String? unitFloor;
  int? listingPrice;
  String? propertyType;

  // Descrizione
  String? description;

  // Planimetria
  late List planimetry;
  late bool wantsNewarcPlanimetry;

  late List capitolato;

  // Fotografie
  late List pictures;
  late bool wantsNewarcPictures;

  // Dotazioni e particolarità
  bool? elevator;
  bool? hasCantina;
  bool? airConditioning;
  bool? securityDoor;
  bool? fiber;
  bool? tvStation;
  bool? highEfficiencyFrames;
  bool? alarm;
  bool? terrace;
  bool? sharedGarden;
  bool? privateGarden;
  bool? hasConcierge;
  bool? motorizedSunblind;
  bool? domotizedSunblind;
  bool? domotizedLights;
  bool? centralizedHeating;
  bool? autonomousHeating;
  bool? highFloor;
  bool? metroVicinity;
  bool? bigBalconies;
  bool? doubleEsposition;
  bool? tripleEsposition;
  bool? quadrupleEsposition;
  bool? bigLiving;
  bool? doubleBathroom;
  bool? nobleBuilding;
  bool? surveiledBuilding;
  bool? swimmingPool;
  bool? hasGarage;
  String? energyClass;
  int? constructionYear;
  late List externalEsposition;
  bool? solarPanel;
  bool? walkInCloset;

  // Indicazioni speciali
  bool? hasSpecialHints;
  String? specialHints;
  String? propertyUpForSaleAnswer;

  // Pagamento
  bool wantsWatermark = true;
  late bool receivedPayment;
  int? receivedPaymentDate;
  String? receivedPaymentReference;

  // Indicatori avanzamento richiesta cliente
  late String requestStatus; // among ['da completare', 'in analisi', 'bloccata', 'preventivazione', 'confermata']
  String? blockedSection;
  String? blockedNotes;

  // Assegnazione team
  late List renderistTeamIdList;
  List<JobComments>? renderistTeamNotes;

  // Prodotti Newarc Immagina
  late List immaginaPictures;
  late List immaginaRenders;
  late List immaginaVideoRender;
  late List immaginaPlanimetry;
  late List immaginaAdPictures;
  String? immaginaBrochure;
  String? immaginaVirtualTour;
  int? renovationPrice;

  // Indicatori avanzamento progetto immagina
  String? immaginaProjectStatus; // among ['lavori in corso', 'completato']
  // Dati vendita
  int? sellingPrice;
  int? sellingDate;
  String? acquirerName;
  String? acquirerPhoneNumber;
  String? acquirerEmailId;
  late bool isHouseSold;
  late bool isRenovationSold;

  String? priority;

  // Immagina for professionals
  bool isForProfessionals = false;
  List<ImmaginaProject>? childrenProjects;
  // Materiali
  late bool wantsMaterialNotes;
  String? materialsNotes;
  // Optionals
  late List optionals;

  // Immagina smart
  bool isSmart = false;
  String? smartServiceType = null; // among ['nakedInteriors', 'furnishedOriginal', 'furnishedRenewed']
  String? smartServiceStyle = null; // among ['Rome', 'Tokyo', 'Stockholm', 'Avignone', 'New York', 'Paris']
  late List<GeneratedDictionary> generations;

  ImmaginaProject(Map<String, dynamic> immaginaProjectMap) {

    this.priority = immaginaProjectMap['priority'];
    this.receivedSuccessFeePaymentDate = immaginaProjectMap['receivedSuccessFeePaymentDate'];

    this.isPaidWithCredits = immaginaProjectMap['isPaidWithCredits'];
    this.appliedSuccessFee = immaginaProjectMap['appliedSuccessFee'];
    
    this.isArchived = immaginaProjectMap['isArchived'];
    this.propertyId = immaginaProjectMap['propertyId'];
    
    this.isAgencyArchived = immaginaProjectMap['isAgencyArchived'];
    this.isWorkArchived = immaginaProjectMap['isWorkArchived'];
    this.statusChangedDate = immaginaProjectMap['statusChangedDate'];
    this.projectCompletedDate = immaginaProjectMap['projectCompletedDate'];
    this.housingUnit = immaginaProjectMap['housingUnit'];
    this.id = immaginaProjectMap['id'];
    this.insertionTimestamp = immaginaProjectMap['insertionTimestamp'];
    this.agencyId = immaginaProjectMap['agencyId'];
    this.projectId = immaginaProjectMap['projectId'] ?? "";
    this.professionalId = immaginaProjectMap['professionalId'];
    this.isThroughAgency = immaginaProjectMap['isThroughAgency'] ?? false;
    this.suggestedAgency = immaginaProjectMap['suggestedAgency'] != null ? SuggestedAgency(immaginaProjectMap['suggestedAgency']) : null;
    this.streetName = immaginaProjectMap['streetName'];
    this.streetNumber = immaginaProjectMap['streetNumber'];
    this.city = immaginaProjectMap['city'];
    this.marketZone = immaginaProjectMap['marketZone'];
    this.province = immaginaProjectMap['province'];
    this.region = immaginaProjectMap['region'];
    this.country = immaginaProjectMap['country'];
    this.postalCode = immaginaProjectMap['postalCode'];
    this.fullAddress = immaginaProjectMap['fullAddress'];
    this.latitude = immaginaProjectMap['latitude'];
    this.longitude = immaginaProjectMap['longitude'];
    this.addressInfo = (immaginaProjectMap.containsKey('addressInfo') && immaginaProjectMap['addressInfo'] != null) ? BaseAddressInfo.fromMap(immaginaProjectMap['addressInfo']) : null;
    this.grossSquareFootage = immaginaProjectMap['grossSquareFootage'];
    this.rooms = immaginaProjectMap['rooms'];
    this.numberOfBathrooms = immaginaProjectMap['numberOfBathrooms'];
    this.unitFloor = immaginaProjectMap['unitFloor'];
    this.listingPrice = immaginaProjectMap['listingPrice'];
    this.propertyType = immaginaProjectMap['propertyType'];
    this.description = immaginaProjectMap['description'];
    this.planimetry = immaginaProjectMap['planimetry'];
    this.capitolato = immaginaProjectMap['capitolato'] ?? [];
    this.wantsNewarcPlanimetry = immaginaProjectMap['wantsNewarcPlanimetry'];
    this.pictures = immaginaProjectMap['pictures'];
    this.wantsNewarcPictures = immaginaProjectMap['wantsNewarcPictures'];
    this.elevator = immaginaProjectMap['elevator'];
    this.hasCantina = immaginaProjectMap['hasCantina'];
    this.airConditioning = immaginaProjectMap['airConditioning'];
    this.securityDoor = immaginaProjectMap['securityDoor'];
    this.fiber = immaginaProjectMap['fiber'];
    this.tvStation = immaginaProjectMap['tvStation'];
    this.highEfficiencyFrames = immaginaProjectMap['highEfficiencyFrames'];
    this.alarm = immaginaProjectMap['alarm'];
    this.terrace = immaginaProjectMap['terrace'];
    this.sharedGarden = immaginaProjectMap['sharedGarden'];
    this.privateGarden = immaginaProjectMap['privateGarden'];
    this.hasConcierge = immaginaProjectMap['hasConcierge'];
    this.motorizedSunblind = immaginaProjectMap['motorizedSunblind'];
    this.domotizedSunblind = immaginaProjectMap['domotizedSunblind'];
    this.domotizedLights = immaginaProjectMap['domotizedLights'];
    this.centralizedHeating = immaginaProjectMap['centralizedHeating'];
    this.autonomousHeating = immaginaProjectMap['autonomousHeating'];
    this.highFloor = immaginaProjectMap['highFloor'];
    this.metroVicinity = immaginaProjectMap['metroVicinity'];
    this.bigBalconies = immaginaProjectMap['bigBalconies'];
    this.doubleEsposition = immaginaProjectMap['doubleEsposition'];
    this.tripleEsposition = immaginaProjectMap['tripleEsposition'];
    this.quadrupleEsposition = immaginaProjectMap['quadrupleEsposition'];
    this.bigLiving = immaginaProjectMap['bigLiving'];
    this.doubleBathroom = immaginaProjectMap['doubleBathroom'];
    this.nobleBuilding = immaginaProjectMap['nobleBuilding'];
    this.surveiledBuilding = immaginaProjectMap['surveiledBuilding'];
    this.hasGarage = immaginaProjectMap['hasGarage'];
    this.swimmingPool = immaginaProjectMap['swimmingPool'];
    this.energyClass = immaginaProjectMap['energyClass'];
    this.constructionYear = immaginaProjectMap['constructionYear'];
    this.externalEsposition = immaginaProjectMap['externalEsposition'];
    this.solarPanel = immaginaProjectMap['solarPanel'];
    this.walkInCloset = immaginaProjectMap['walkInCloset'];
    this.hasSpecialHints = immaginaProjectMap['hasSpecialHints'];
    this.specialHints = immaginaProjectMap['specialHints'];
    this.wantsWatermark = immaginaProjectMap['wantsWatermark'] ?? true;
    this.receivedPayment = immaginaProjectMap['receivedPayment'];
    this.receivedPaymentDate = immaginaProjectMap['receivedPaymentDate'];
    this.receivedPaymentReference = immaginaProjectMap['receivedPaymentReference'];
    this.requestStatus = immaginaProjectMap['requestStatus'];
    this.blockedSection = immaginaProjectMap['blockedSection'];
    this.blockedNotes = immaginaProjectMap['blockedNotes'];
    this.renderistTeamIdList = (immaginaProjectMap.containsKey('renderistTeamIdList') && immaginaProjectMap['renderistTeamIdList'] != null) ? immaginaProjectMap['renderistTeamIdList'] : [];
    this.renderistTeamNotes = (immaginaProjectMap.containsKey('renderistTeamNotes') && immaginaProjectMap['renderistTeamNotes'] != null) 
            ? List<JobComments>.from(immaginaProjectMap['renderistTeamNotes'].map((note) => JobComments.fromDocument(note, note["index"])))
            : null;
    this.immaginaPictures = immaginaProjectMap['immaginaPictures'] ?? [];
    this.immaginaRenders = immaginaProjectMap['immaginaRenders'] ?? [];
    this.immaginaVideoRender = immaginaProjectMap['immaginaVideoRender'] ?? [];
    this.immaginaPlanimetry = immaginaProjectMap['immaginaPlanimetry'] ?? [];
    this.immaginaAdPictures = immaginaProjectMap['immaginaAdPictures'] ?? [];
    this.immaginaBrochure = immaginaProjectMap['immaginaBrochure'];
    this.immaginaVirtualTour = immaginaProjectMap['immaginaVirtualTour'];
    this.renovationPrice = immaginaProjectMap['renovationPrice'];
    this.immaginaProjectStatus = immaginaProjectMap['immaginaProjectStatus'];
    this.sellingPrice = immaginaProjectMap['sellingPrice'];
    this.sellingDate = immaginaProjectMap['sellingDate'];
    this.acquirerName = immaginaProjectMap['acquirerName'];
    this.acquirerPhoneNumber = immaginaProjectMap['acquirerPhoneNumber'];
    this.acquirerEmailId = immaginaProjectMap['acquirerEmailId'];
    this.isHouseSold = immaginaProjectMap['isHouseSold'];
    this.isRenovationSold = immaginaProjectMap['isRenovationSold'];
    this.propertyUpForSaleAnswer = immaginaProjectMap['propertyUpForSaleAnswer'];
    this.isForProfessionals = immaginaProjectMap['isForProfessionals'] ?? false;
    this.childrenProjects = (immaginaProjectMap.containsKey('childrenProjects') && immaginaProjectMap['childrenProjects'] != null)
            ? List<ImmaginaProject>.from(immaginaProjectMap['childrenProjects'].map((proj) => ImmaginaProject(proj)))
            : null;
    this.optionals = immaginaProjectMap['optionals'] ?? [];
    this.wantsMaterialNotes = immaginaProjectMap['wantsMaterialNotes'] ?? true;
    this.materialsNotes = immaginaProjectMap['materialsNotes'];
    this.isSmart = immaginaProjectMap['isSmart'] ?? false;
    this.smartServiceType = immaginaProjectMap['smartServiceType'];
    this.smartServiceStyle = immaginaProjectMap['smartServiceStyle'];
    this.generations = List<GeneratedDictionary>.from((immaginaProjectMap['generations'] ?? []).map((proj) => GeneratedDictionary.fromMap(proj)));
  }

  getid(){
    return this.id;
  }

  ImmaginaProject.empty() {
    this.priority = "";
    this.propertyUpForSaleAnswer = "";
    this.receivedSuccessFeePaymentDate = null;

    this.isPaidWithCredits = null;
    this.appliedSuccessFee = null;

    this.isArchived = false;
    this.propertyId = null;
    this.isAgencyArchived = false;
    this.isWorkArchived = false;
    this.statusChangedDate = null;
    this.projectCompletedDate = null;
    this.housingUnit = null;

    this.id = '';
    this.insertionTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.agencyId = '';
    this.projectId = '';
    this.isThroughAgency = true;
    this.suggestedAgency = null;
    this.professionalId = null;
    this.streetName = null;
    this.streetNumber = null;
    this.city = null;
    this.marketZone = null;
    this.province = null;
    this.region = null;
    this.country = null;
    this.postalCode = null;
    this.fullAddress = null;
    this.latitude = null;
    this.longitude = null;
    this.addressInfo = BaseAddressInfo.empty();
    this.grossSquareFootage = null;
    this.rooms = null;
    this.numberOfBathrooms = null;
    this.unitFloor = null;
    this.listingPrice = null;
    this.propertyType = null;
    this.description = null;
    this.planimetry = [];
    this.capitolato = [];
    this.wantsNewarcPlanimetry = false;
    this.pictures = [];
    this.wantsNewarcPictures = false;
    this.elevator = null;
    this.hasCantina = null;
    this.airConditioning = null;
    this.securityDoor = null;
    this.fiber = null;
    this.tvStation = null;
    this.highEfficiencyFrames = null;
    this.alarm = null;
    this.terrace = null;
    this.sharedGarden = null;
    this.privateGarden = null;
    this.hasConcierge = null;
    this.motorizedSunblind = null;
    this.domotizedSunblind = null;
    this.domotizedLights = null;
    this.centralizedHeating = null;
    this.autonomousHeating = null;
    this.highFloor = null;
    this.metroVicinity = null;
    this.bigBalconies = null;
    this.doubleEsposition = null;
    this.tripleEsposition = null;
    this.quadrupleEsposition = null;
    this.bigLiving = null;
    this.doubleBathroom = null;
    this.nobleBuilding = null;
    this.surveiledBuilding = null;
    this.swimmingPool = null;
    this.hasGarage = null;
    this.energyClass = null;
    this.constructionYear = null;
    this.externalEsposition = [];
    this.solarPanel = null;
    this.walkInCloset = null;
    this.hasSpecialHints = null;
    this.specialHints = null;
    this.wantsWatermark = true;
    this.receivedPayment = false;
    this.receivedPaymentDate = null;
    this.receivedPaymentReference = null;
    this.requestStatus = 'da completare';
    this.blockedSection = null;
    this.blockedNotes = null;
    this.renderistTeamIdList = [];
    this.renderistTeamNotes = null;
    this.immaginaPictures = [];
    this.immaginaRenders = [];
    this.immaginaVideoRender = [];
    this.immaginaPlanimetry = [];
    this.immaginaAdPictures = [];
    this.immaginaBrochure = null;
    this.immaginaVirtualTour = null;
    this.renovationPrice = null;
    this.immaginaProjectStatus = null;
    this.sellingPrice = null;
    this.sellingDate = null;
    this.acquirerName = null;
    this.acquirerPhoneNumber = null;
    this.isHouseSold = false;
    this.isRenovationSold = false;
    this.acquirerEmailId = "";
    this.isForProfessionals = false;
    this.childrenProjects = null;
    this.optionals = [];
    this.wantsMaterialNotes = true;
    this.materialsNotes = null;
    this.isSmart = false;
    this.smartServiceType = null;
    this.smartServiceStyle = null;
    this.generations = [];
  }

  ImmaginaProject.fromDocument(Map<String, dynamic> data, String id) {

    print({'myid', id});

    this.id = id;
    
    this.priority = data['priority'] ?? "";
    this.propertyUpForSaleAnswer = data['propertyUpForSaleAnswer'] ?? "";
    this.receivedSuccessFeePaymentDate = data['receivedSuccessFeePaymentDate'] ?? null;

    this.isPaidWithCredits = data['isPaidWithCredits'] ?? null;
    this.appliedSuccessFee = data['appliedSuccessFee'] ?? null;

    this.isArchived = data['isArchived'] ?? false;
    this.propertyId = data['propertyId'] ?? null;
    
    this.isAgencyArchived = data['isAgencyArchived'] ?? false;
    this.isWorkArchived = data['isWorkArchived'] ?? false;
    this.statusChangedDate = data['statusChangedDate'] ?? null;
    this.projectCompletedDate = data['projectCompletedDate'] ?? null;
    this.housingUnit = data['housingUnit'] ?? null;

    this.insertionTimestamp = data['insertionTimestamp'] ?? Timestamp.now().millisecondsSinceEpoch;
    this.agencyId = data['agencyId'] ?? '';
    this.projectId = data['projectId'] ?? '';
    this.isThroughAgency = data['isThroughAgency'] ?? false;
    this.suggestedAgency = data['suggestedAgency'] != null ? SuggestedAgency(data['suggestedAgency']) : null;
    this.professionalId = data['professionalId'];
    this.streetName = data['streetName'];
    this.streetNumber = data['streetNumber'];
    this.city = data['city'];
    this.marketZone = data['marketZone'];
    this.province = data['province'];
    this.region = data['region'];
    this.country = data['country'];
    this.postalCode = data['postalCode'];
    this.fullAddress = data['fullAddress'];
    this.latitude = data['latitude'];
    this.longitude = data['longitude'];
    this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
    this.grossSquareFootage = data['grossSquareFootage'];
    this.rooms = data['rooms'];
    this.numberOfBathrooms = data['numberOfBathrooms'];
    this.unitFloor = data['unitFloor'];
    this.listingPrice = data['listingPrice'];
    this.propertyType = data['propertyType'];
    this.description = data['description'];
    this.planimetry = data['planimetry'] ?? [];
    this.capitolato = data['capitolato'] ?? [];
    this.wantsNewarcPlanimetry = data['wantsNewarcPlanimetry'] ?? false;
    this.pictures = data['pictures'] ?? [];
    this.wantsNewarcPictures = data['wantsNewarcPictures'] ?? false;
    this.elevator = data['elevator'];
    this.hasCantina = data['hasCantina'];
    this.airConditioning = data['airConditioning'];
    this.securityDoor = data['securityDoor'];
    this.fiber = data['fiber'];
    this.tvStation = data['tvStation'];
    this.highEfficiencyFrames = data['highEfficiencyFrames'];
    this.alarm = data['alarm'];
    this.terrace = data['terrace'];
    this.sharedGarden = data['sharedGarden'];
    this.privateGarden = data['privateGarden'];
    this.hasConcierge = data['hasConcierge'];
    this.motorizedSunblind = data['motorizedSunblind'];
    this.domotizedSunblind = data['domotizedSunblind'];
    this.domotizedLights = data['domotizedLights'];
    this.centralizedHeating = data['centralizedHeating'];
    this.autonomousHeating = data['autonomousHeating'];
    this.highFloor = data['highFloor'];
    this.metroVicinity = data['metroVicinity'];
    this.bigBalconies = data['bigBalconies'];
    this.doubleEsposition = data['doubleEsposition'];
    this.tripleEsposition = data['tripleEsposition'];
    this.quadrupleEsposition = data['quadrupleEsposition'];
    this.bigLiving = data['bigLiving'];
    this.doubleBathroom = data['doubleBathroom'];
    this.nobleBuilding = data['nobleBuilding'];
    this.surveiledBuilding = data['surveiledBuilding'];
    this.swimmingPool = data['swimmingPool'];
    this.hasGarage = data['hasGarage'];
    this.energyClass = data['energyClass'];
    this.constructionYear = data['constructionYear'];
    this.externalEsposition = data['externalEsposition'] ?? [];
    this.solarPanel = data['solarPanel'];
    this.walkInCloset = data['walkInCloset'];
    this.hasSpecialHints = data['hasSpecialHints'];
    this.specialHints = data['specialHints'];
    this.wantsWatermark = data['wantsWatermark'] ?? true;
    this.receivedPayment = data['receivedPayment'] ?? false;
    this.receivedPaymentDate = data['receivedPaymentDate'];
    this.receivedPaymentReference = data['receivedPaymentReference'];
    this.requestStatus = data['requestStatus'] ?? 'da completare';
    this.blockedSection = data['blockedSection'];
    this.blockedNotes = data['blockNotes'];
    this.renderistTeamIdList = (data.containsKey('renderistTeamIdList') && data['renderistTeamIdList'] != null) ? data['renderistTeamIdList'] : [];
    this.renderistTeamNotes = (data.containsKey('renderistTeamNotes') && data['renderistTeamNotes'] != null) 
        ? List<JobComments>.from(data['renderistTeamNotes'].map((note) => JobComments.fromDocument(note, note["index"])))
        : null;
    this.immaginaPictures = data['immaginaPictures'] ?? [];
    this.immaginaRenders = data['immaginaRenders'] ?? [];
    this.immaginaVideoRender = data['immaginaVideoRender'] ?? [];
    this.immaginaPlanimetry = data['immaginaPlanimetry'] ?? [];
    this.immaginaAdPictures = data['immaginaAdPictures'] ?? [];
    this.immaginaBrochure = data['immaginaBrochure'];
    this.immaginaVirtualTour = data['immaginaVirtualTour'];
    this.renovationPrice = data['renovationPrice'];
    this.immaginaProjectStatus = data['immaginaProjectStatus'];
    this.sellingPrice = data['sellingPrice'];
    this.sellingDate = data['sellingDate'];
    this.acquirerName = data['acquirerName'];
    this.acquirerPhoneNumber = data['acquirerPhoneNumber'];
    this.isHouseSold = data['isHouseSold'];
    this.isRenovationSold = data['isRenovationSold'];
    this.acquirerEmailId = data['acquirerEmailId'];
    this.isForProfessionals = data['isForProfessionals'] ?? false;
    this.childrenProjects = (data.containsKey('childrenProjects') && data['childrenProjects'] != null)
            ? List<ImmaginaProject>.from(data['childrenProjects'].map((proj) => ImmaginaProject(proj)))
            : null;
    this.optionals = data['optionals'] ?? [];
    this.wantsMaterialNotes = data['wantsMaterialNotes'] ?? true;
    this.materialsNotes = data['materialsNotes'];
    this.isSmart = data['isSmart'] ?? false;
    this.smartServiceType = data['smartServiceType'];
    this.smartServiceStyle = data['smartServiceStyle'];
    this.generations = List<GeneratedDictionary>.from((data['generations'] ?? []).map((proj) => GeneratedDictionary.fromMap(proj)));
  }

  copy(ImmaginaProject immaginaProject) {
    this.priority = immaginaProject.priority;
    this.propertyUpForSaleAnswer = immaginaProject.propertyUpForSaleAnswer;
    this.receivedSuccessFeePaymentDate = immaginaProject.receivedSuccessFeePaymentDate;

    this.isPaidWithCredits = immaginaProject.isPaidWithCredits;
    this.appliedSuccessFee = immaginaProject.appliedSuccessFee;

    this.isArchived = immaginaProject.isArchived;
    this.propertyId = immaginaProject.propertyId;
    
    this.isAgencyArchived = immaginaProject.isAgencyArchived;
    this.isWorkArchived = immaginaProject.isWorkArchived;
    this.statusChangedDate = immaginaProject.statusChangedDate;
    this.projectCompletedDate = immaginaProject.projectCompletedDate;
    this.housingUnit = immaginaProject.housingUnit;

    this.id = immaginaProject.id;
    this.insertionTimestamp = immaginaProject.insertionTimestamp;
    this.agencyId = immaginaProject.agencyId;
    this.projectId = immaginaProject.projectId;
    this.professionalId = immaginaProject.professionalId;
    this.isThroughAgency = immaginaProject.isThroughAgency ?? false;
    this.suggestedAgency = immaginaProject.suggestedAgency;
    this.streetName = immaginaProject.streetName;
    this.streetNumber = immaginaProject.streetNumber;
    this.city = immaginaProject.city;
    this.marketZone = immaginaProject.marketZone;
    this.province = immaginaProject.province;
    this.region = immaginaProject.region;
    this.country = immaginaProject.country;
    this.postalCode = immaginaProject.postalCode;
    this.fullAddress = immaginaProject.fullAddress;
    this.latitude = immaginaProject.latitude;
    this.longitude = immaginaProject.longitude;
    this.addressInfo = immaginaProject.addressInfo;
    this.grossSquareFootage = immaginaProject.grossSquareFootage;
    this.rooms = immaginaProject.rooms;
    this.numberOfBathrooms = immaginaProject.numberOfBathrooms;
    this.unitFloor = immaginaProject.unitFloor;
    this.listingPrice = immaginaProject.listingPrice;
    this.propertyType = immaginaProject.propertyType;
    this.description = immaginaProject.description;
    this.planimetry = immaginaProject.planimetry;
    this.capitolato = immaginaProject.capitolato;
    this.wantsNewarcPlanimetry = immaginaProject.wantsNewarcPlanimetry;
    this.pictures = immaginaProject.pictures;
    this.wantsNewarcPictures = immaginaProject.wantsNewarcPictures;
    this.elevator = immaginaProject.elevator;
    this.hasCantina = immaginaProject.hasCantina;
    this.airConditioning = immaginaProject.airConditioning;
    this.securityDoor = immaginaProject.securityDoor;
    this.fiber = immaginaProject.fiber;
    this.tvStation = immaginaProject.tvStation;
    this.highEfficiencyFrames = immaginaProject.highEfficiencyFrames;
    this.alarm = immaginaProject.alarm;
    this.terrace = immaginaProject.terrace;
    this.sharedGarden = immaginaProject.sharedGarden;
    this.privateGarden = immaginaProject.privateGarden;
    this.hasConcierge = immaginaProject.hasConcierge;
    this.motorizedSunblind = immaginaProject.motorizedSunblind;
    this.domotizedSunblind = immaginaProject.domotizedSunblind;
    this.domotizedLights = immaginaProject.domotizedLights;
    this.centralizedHeating = immaginaProject.centralizedHeating;
    this.autonomousHeating = immaginaProject.autonomousHeating;
    this.highFloor = immaginaProject.highFloor;
    this.metroVicinity = immaginaProject.metroVicinity;
    this.bigBalconies = immaginaProject.bigBalconies;
    this.doubleEsposition = immaginaProject.doubleEsposition;
    this.tripleEsposition = immaginaProject.tripleEsposition;
    this.quadrupleEsposition = immaginaProject.quadrupleEsposition;
    this.bigLiving = immaginaProject.bigLiving;
    this.doubleBathroom = immaginaProject.doubleBathroom;
    this.nobleBuilding = immaginaProject.nobleBuilding;
    this.surveiledBuilding = immaginaProject.surveiledBuilding;
    this.swimmingPool = immaginaProject.swimmingPool;
    this.hasGarage = immaginaProject.hasGarage;
    this.energyClass = immaginaProject.energyClass;
    this.constructionYear = immaginaProject.constructionYear;
    this.externalEsposition = immaginaProject.externalEsposition;
    this.solarPanel = immaginaProject.solarPanel;
    this.walkInCloset = immaginaProject.walkInCloset;
    this.hasSpecialHints = immaginaProject.hasSpecialHints;
    this.specialHints = immaginaProject.specialHints;
    this.wantsWatermark = immaginaProject.wantsWatermark;
    this.receivedPayment = immaginaProject.receivedPayment;
    this.receivedPaymentDate = immaginaProject.receivedPaymentDate;
    this.receivedPaymentReference = immaginaProject.receivedPaymentReference;
    this.requestStatus = immaginaProject.requestStatus;
    this.blockedSection = immaginaProject.blockedSection;
    this.blockedNotes = immaginaProject.blockedNotes;
    this.renderistTeamIdList = immaginaProject.renderistTeamIdList;
    this.renderistTeamNotes = immaginaProject.renderistTeamNotes;
    this.immaginaPictures = immaginaProject.immaginaPictures;
    this.immaginaRenders = immaginaProject.immaginaRenders;
    this.immaginaVideoRender = immaginaProject.immaginaVideoRender;
    this.immaginaPlanimetry = immaginaProject.immaginaPlanimetry;
    this.immaginaAdPictures = immaginaProject.immaginaAdPictures;
    this.immaginaBrochure = immaginaProject.immaginaBrochure;
    this.immaginaVirtualTour = immaginaProject.immaginaVirtualTour;
    this.renovationPrice = immaginaProject.renovationPrice;
    this.immaginaProjectStatus = immaginaProject.immaginaProjectStatus;
    this.sellingPrice = immaginaProject.sellingPrice;
    this.sellingDate = immaginaProject.sellingDate;
    this.acquirerName = immaginaProject.acquirerName;
    this.acquirerPhoneNumber = immaginaProject.acquirerPhoneNumber;
    this.isHouseSold = immaginaProject.isHouseSold;
    this.isRenovationSold = immaginaProject.isRenovationSold;
    this.acquirerEmailId = immaginaProject.acquirerEmailId;
    this.isForProfessionals = immaginaProject.isForProfessionals;
    this.childrenProjects = immaginaProject.childrenProjects;
    this.optionals = immaginaProject.optionals;
    this.wantsMaterialNotes = immaginaProject.wantsMaterialNotes;
    this.materialsNotes = immaginaProject.materialsNotes;
    this.isSmart = immaginaProject.isSmart;
    this.smartServiceType = immaginaProject.smartServiceType;
    this.smartServiceStyle = immaginaProject.smartServiceStyle;
    this.generations = immaginaProject.generations;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'priority': this.priority,
      'propertyUpForSaleAnswer': this.propertyUpForSaleAnswer,
      'receivedSuccessFeePaymentDate': this.receivedSuccessFeePaymentDate,
      'isPaidWithCredits': this.isPaidWithCredits,
      'appliedSuccessFee': this.appliedSuccessFee,
      'isArchived': this.isArchived,
      'propertyId': this.propertyId??null,
      'isAgencyArchived': this.isAgencyArchived,
      'isWorkArchived': this.isWorkArchived,
      'housingUnit': this.housingUnit,
      'statusChangedDate': this.statusChangedDate,
      'projectCompletedDate': this.projectCompletedDate,
      'insertionTimestamp': this.insertionTimestamp,
      'agencyId': this.agencyId,
      'projectId': this.projectId,
      'professionalId': this.professionalId,
      'isThroughAgency': this.isThroughAgency,
      'suggestedAgency': this.suggestedAgency?.toMap(),
      'streetName': this.streetName,
      'streetNumber': this.streetNumber,
      'city': this.city,
      'marketZone': this.marketZone,
      'province': this.province,
      'region': this.region,
      'country': this.country,
      'postalCode': this.postalCode,
      'fullAddress': this.fullAddress,
      'latitude': this.latitude,
      'longitude': this.longitude,
      'addressInfo': this.addressInfo?.toMap(),
      'grossSquareFootage': this.grossSquareFootage,
      'rooms': this.rooms,
      'numberOfBathrooms': this.numberOfBathrooms,
      'unitFloor': this.unitFloor,
      'listingPrice': this.listingPrice,
      'propertyType': this.propertyType,
      'description': this.description,
      'planimetry': this.planimetry,
      'capitolato': this.capitolato,
      'wantsNewarcPlanimetry': this.wantsNewarcPlanimetry,
      'pictures': this.pictures,
      'wantsNewarcPictures': this.wantsNewarcPictures,
      'elevator': this.elevator,
      'hasCantina': this.hasCantina,
      'airConditioning': this.airConditioning,
      'securityDoor': this.securityDoor,
      'fiber': this.fiber,
      'tvStation': this.tvStation,
      'highEfficiencyFrames': this.highEfficiencyFrames,
      'alarm': this.alarm,
      'terrace': this.terrace,
      'sharedGarden': this.sharedGarden,
      'privateGarden': this.privateGarden,
      'hasConcierge': this.hasConcierge,
      'motorizedSunblind': this.motorizedSunblind,
      'domotizedSunblind': this.domotizedSunblind,
      'domotizedLights': this.domotizedLights,
      'centralizedHeating': this.centralizedHeating,
      'autonomousHeating': this.autonomousHeating,
      'highFloor': this.highFloor,
      'metroVicinity': this.metroVicinity,
      'bigBalconies': this.bigBalconies,
      'doubleEsposition': this.doubleEsposition,
      'tripleEsposition': this.tripleEsposition,
      'quadrupleEsposition': this.quadrupleEsposition,
      'bigLiving': this.bigLiving,
      'doubleBathroom': this.doubleBathroom,
      'nobleBuilding': this.nobleBuilding,
      'surveiledBuilding': this.surveiledBuilding,
      'swimmingPool': this.swimmingPool,
      'hasGarage': this.hasGarage,
      'energyClass': this.energyClass,
      'constructionYear': this.constructionYear,
      'externalEsposition': this.externalEsposition,
      'solarPanel': this.solarPanel,
      'walkInCloset': this.walkInCloset,
      'hasSpecialHints': this.hasSpecialHints,
      'specialHints': this.specialHints,
      'wantsWatermark': this.wantsWatermark,
      'receivedPayment': this.receivedPayment,
      'receivedPaymentDate': this.receivedPaymentDate,
      'receivedPaymentReference': this.receivedPaymentReference,
      'requestStatus': this.requestStatus,
      'blockedSection': this.blockedSection,
      'blockNotes': this.blockedNotes,
      'renderistTeamIdList': this.renderistTeamIdList,
      'renderistTeamNotes': this.renderistTeamNotes == null ? null : this.renderistTeamNotes!.map((elem) => elem.toMap()).toList(),
      'immaginaPicttures': this.immaginaPictures,
      'immaginaRenders': this.immaginaRenders,
      'immaginaVideoRender': this.immaginaVideoRender,
      'immaginaPlanimetry': this.immaginaPlanimetry,
      'immaginaAdPictures': this.immaginaAdPictures,
      'immaginaBrochure': this.immaginaBrochure,
      'immaginaVirtualTour': this.immaginaVirtualTour,
      'renovationPrice': this.renovationPrice,
      'immaginaProjectStatus': this.immaginaProjectStatus,
      'sellingPrice': this.sellingPrice,
      'sellingDate': this.sellingDate,
      'acquirerName': this.acquirerName,
      'acquirerPhoneNumber': this.acquirerPhoneNumber,
      'isHouseSold': this.isHouseSold,
      'isRenovationSold': this.isRenovationSold,
      'acquirerEmailId': this.acquirerEmailId,
      'isForProfessionals': this.isForProfessionals,
      'childrenProjects': this.childrenProjects?.map((e) => e.toMap()).toList() ?? null,
      'optionals': this.optionals,
      'wantsMaterialNotes': this.wantsMaterialNotes,
      'materialsNotes': this.materialsNotes,
      'isSmart': this.isSmart,
      'smartServiceType': this.smartServiceType,
      'smartServiceStyle': this.smartServiceStyle,
      'generations': this.generations.map((e) => e.toMap()).toList(),
    };
  }

  bool localizzazione() {
    bool response = false;
    if (this.addressInfo == null) {
      if (((this.streetName != null) & (this.streetName != "")) &
        ((this.streetNumber != null) & (this.streetNumber != "")) &
        ((this.city != null) & (this.city != ""))) {
        response = true;
      }
    } else {
      if (this.addressInfo!.streetName != null && this.addressInfo!.streetName != "" &&
          this.addressInfo!.streetNumber != null && this.addressInfo!.streetNumber != "" &&
          this.addressInfo!.city != null && this.addressInfo!.city != "" &&
          this.addressInfo!.region != null && this.addressInfo!.region != "") {
        response = true;
      }
    }
    return response;
  }

  bool linkingAgency() {
    bool response = false;
    if (this.isThroughAgency == true && this.agencyId != null && this.agencyId != "") {
      response = true;
    } else if (this.isThroughAgency == true && this.suggestedAgency != null && this.suggestedAgency!.complete()) {
      response = true;
    } else if (this.isThroughAgency == false) {
      response = true;
    }
    return response;
  }

  bool info_generali() {
    bool response = false;
    if ((this.grossSquareFootage != null) 
        & (this.rooms != null) 
        & (this.numberOfBathrooms != null) 
        & (this.unitFloor != null) 
        & (this.listingPrice != null)
        & (this.propertyType != null)) {
      response = true;
    }
    return response;
  }

  bool descrizione() {
    return description != null && description != "";
  }

  bool planimetria() {
    return planimetry.isNotEmpty || wantsNewarcPlanimetry == true;
  }

  bool fotografie() {
    bool areImagesTagged = true;
    for (var elem in pictures) {
      if (
        !elem.containsKey('tag') ||
        elem['tag'] == null ||
        elem['tag'] == ""
        ){
        areImagesTagged = false;
        break;
      }
    }
    return wantsNewarcPictures == true || (pictures.isNotEmpty && areImagesTagged);
  }

  bool checkDotazioni() {
    // check checkbox
    bool response = false;
    List<bool?> fields = [
      this.elevator,
      this.hasCantina,
      this.airConditioning,
      this.securityDoor,
      this.tvStation,
      this.highEfficiencyFrames,
      this.alarm,
      this.terrace,
      this.sharedGarden,
      this.privateGarden,
      this.hasConcierge,
      this.motorizedSunblind,
      this.domotizedSunblind,
      this.domotizedLights,
      this.centralizedHeating,
      this.autonomousHeating,
      this.highFloor,
      this.metroVicinity,
      this.bigBalconies,
      this.doubleEsposition,
      this.tripleEsposition,
      this.quadrupleEsposition,
      this.bigLiving,
      this.doubleBathroom,
      this.nobleBuilding,
      this.surveiledBuilding,
      this.swimmingPool,
      this.hasGarage,
      this.solarPanel,
      this.walkInCloset,
    ];
    if (fields.any((field) => field == true)) {
      response = true;
    }
    // check other characteristics
    if (!(
      (this.energyClass != null && this.energyClass != "") &&
      (this.constructionYear != null && this.constructionYear != "") &&
      this.externalEsposition.isNotEmpty
      )){
        response = false;
      }
    return response;
  }

  bool checkOptionals() {
    return optionals.isNotEmpty;
  }

  bool checkMaterials() {
    return (!wantsMaterialNotes) || (materialsNotes != null && materialsNotes != "");
  }

  bool info_aggiuntive() {
    bool response = false;
    log("this.propertyUpForSaleAnswer ===> ${this.propertyUpForSaleAnswer}");
    if (this.hasSpecialHints == false && ((this.propertyUpForSaleAnswer != null) && (this.propertyUpForSaleAnswer != ''))) {
      response = true;
    } else if (this.hasSpecialHints == true) {
      if (((this.specialHints != null) && (this.specialHints != '')) && ((this.propertyUpForSaleAnswer != null) && (this.propertyUpForSaleAnswer != ''))) {
        response = true;
      }
    }
    return response;
  }

  bool pagamento() {
    return receivedPayment && receivedPaymentDate != null ;
  }

  // Smart
  bool checkSmartServiceType() {
    return smartServiceType != null;
  }

  bool checkSmartServiceStyle() {
    if (smartServiceType == 'nakedInteriors') {
      return true;
    } else {
      return smartServiceStyle != null;
    }
  }

}


class ImmaginaComparabili {
  String? uid;
  String? bathrooms;
  String? gSF;
  double? latitude;
  double? longitude;
  String? marketZone;
  String? picture;
  double? price;
  String? rooms;
  String? status;
  String? title;
  String? url;
  String? unitFloor;
  bool? isSelected;

  ImmaginaComparabili.fromJson(Map<String, dynamic> data) {
    this.uid = data['uid'] ?? "";
    this.bathrooms = data['bathrooms'] ?? "";
    this.gSF = data['gSF'] ?? "";
    this.latitude = data['latitude'];
    this.longitude = data['longitude'];
    this.marketZone = data['marketZone'] ?? "";
    this.picture = data['picture'] ?? "";
    this.price = data['price'] ?? 0.0;
    this.rooms = data['rooms'] ?? "";
    this.status = data['status'] ?? "";
    this.title = data['title'] ?? "";
    this.url = data['url'] ?? "";
    this.unitFloor = data['unitFloor'] ?? "";
    this.isSelected = data['isSelected'] ?? false;

  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'bathrooms': bathrooms,
      'gSF': gSF,
      'latitude': latitude,
      'longitude': longitude,
      'marketZone': marketZone,
      'picture': picture,
      'price': price,
      'rooms': rooms,
      'status': status,
      'title': title,
      'url': url,
      'unitFloor': unitFloor,
      'isSelected': isSelected,
    };
  }
}

class SuggestedAgency {

  String? name;
  BaseAddressInfo? addressInfo;
  String? email;
  bool converted = false;
  int? conversiontimestamp;

  SuggestedAgency(Map<String, dynamic> data) {
    this.name = data['name'];
    this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
    this.email = data['email'];
    this.converted = data['converted'] ?? false;
    this.conversiontimestamp = data['conversiontimestamp'];
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'addressInfo': addressInfo?.toMap(),
      'email': email,
      'converted': converted,
      'conversiontimestamp': conversiontimestamp,
    };
  }

  SuggestedAgency.empty() {
    this.name = null;
    this.addressInfo = null;
    this.email = null;
    this.converted = false;
    this.conversiontimestamp = null;
  }

  bool complete() {
    return this.name != null && ((this.addressInfo?.isValidAddress() ?? false) == true) && this.email != null;
  }
}

class GeneratedDictionary {
  late String generated_file;
  late String original_file;
  late String tag;
  String? response_id;
  late bool generation_status;
  late String generation_type;
  String? generation_style;
  double? generation_time_s;
  Map? generation_token_usage;
  bool? generation_grade;
  String? generation_grade_msg;

  GeneratedDictionary.fromMap(Map map) {
    generated_file = map['generated_file'];
    original_file = map['original_file'];
    tag = map['tag'];
    response_id = map['response_id'];
    generation_status = map['generation_status'];
    generation_type = map['generation_type'];
    generation_style = map['generation_style'];
    generation_time_s = map['generation_time_s'];
    generation_token_usage = map['generation_token_usage'];
    generation_grade = map['generation_grade'];
    generation_grade_msg = map['generation_grade_msg'];
  }

  Map<String, dynamic> toMap() {
    return {
      'generated_file': generated_file,
      'original_file': original_file,
      'tag': tag,
      'response_id': response_id,
      'generation_status': generation_status,
      'generation_type': generation_type,
      'generation_style': generation_style,
      'generation_time_s': generation_time_s,
      'generation_token_usage': generation_token_usage,
      'generation_grade': generation_grade,
      'generation_grade_msg': generation_grade_msg,
    };
  }
}
