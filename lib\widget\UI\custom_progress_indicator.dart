import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A custom progress indicator that animates icon in assetPath
/// in an aperiodic way similar to CircularProgressIndicator
class CustomProgressIndicator extends StatefulWidget {

  /// The path to the asset image for the icon
  final String assetPath;

  /// The size of the icon
  final double size;
  
  /// The color to apply to the icon (optional)
  final Color? color;
  
  /// The type of animation cycle
  final String animationType;

  /// The duration of animation cycle
  final Duration animationDuration;
  
  /// Whether the animation should be aperiodic (varying speed)
  final bool aperiodic;

  const CustomProgressIndicator({
    Key? key,
    required this.assetPath,
    this.size = 24.0,
    this.color,
    this.animationType = 'rotating',
    this.animationDuration = const Duration(seconds: 2),
    this.aperiodic = true,
  }) : super(key: key);

  @override
  State<CustomProgressIndicator> createState() => _CustomProgressIndicatorState();
}

class _CustomProgressIndicatorState extends State<CustomProgressIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    // Create the rotation animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    if (widget.animationType == 'rotating') {
      _createRotatingAnimation();
    } else if (widget.animationType == 'disappearing') {
      _createDisappearingAnimation();
    }

    // Start the animation and repeat it indefinitely
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _createRotatingAnimation() {
    if (widget.aperiodic) {
      // Create an aperiodic animation using a custom curve
      _animation = Tween<double>(
        begin: 0.0,
        end: 2 * math.pi,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: _AperiodicCurve(),
      ));
    } else {
      // Create a linear rotation animation
      _animation = Tween<double>(
        begin: 0.0,
        end: 2 * math.pi,
      ).animate(_animationController);
    }
  }

  void _createDisappearingAnimation() {
    if (widget.aperiodic) {
      // Create an aperiodic animation using a custom curve
      _animation = Tween<double>(
        begin: 1.0,
        end: 0.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: _AperiodicCurve(),
      ));
    } else {
      // Create a linear disappearing animation
      _animation = Tween<double>(
        begin: 1.0,
        end: 0.0,
      ).animate(_animationController);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          switch (widget.animationType) {
            case 'disappearing':
              return Opacity(
                opacity: _animation.value,
                child: Image.asset(
                  widget.assetPath,
                  width: widget.size,
                  height: widget.size,
                  color: widget.color,
                ),
              );
            case 'rotating':
              return Transform.rotate(
                angle: _animation.value,
                child: Image.asset(
                  widget.assetPath,
                  width: widget.size,
                  height: widget.size,
                  color: widget.color,
                ),
              );
            default:
              return Container();
          }  
        },
      ),
    );
  }
}

/// Custom curve that creates an aperiodic (varying speed) animation
/// similar to how CircularProgressIndicator behaves
class _AperiodicCurve extends Curve {
  @override
  double transform(double t) {
    // Create a varying speed effect using sine waves
    // This creates acceleration and deceleration phases
    return t + 0.1 * math.sin(4 * math.pi * t) * (1 - t);
  }
}
