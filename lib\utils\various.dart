import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/websiteAd.dart';
import 'package:newarc_platform/utils/const.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom-button.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'dart:convert';
import 'dart:js_util' as js_util;
import 'dart:html' as html;

showAlertDialog(BuildContext context, String title, String message,
    {String? buttonText, bool addCancel = false}) async {
  // show the dialog
  var returnVariable = await showDialog(
    context: context,
    builder: (BuildContext context) {
      //return alert;
      return Center(
          child: BaseNewarcPopup(
        title: title,
        column: Text(
          textAlign: TextAlign.center,
          message,
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        buttonText: buttonText,
      ));
    },
  );

  if (returnVariable == null) {
    return false;
  } else
    return returnVariable;
}

showToastNotification(BuildContext context, String message, {int duration = 2,bool error=false,}) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(
        message,
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
      backgroundColor: error ? Colors.red : Colors.green,
      duration: Duration(seconds: duration),
    ),
  );
}
String getFormattedDate(int? millisecondsSinceEpoch) {
  if (millisecondsSinceEpoch == null) {
    return "Da definire";
  }
  final date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  final formatter = DateFormat("dd/MM/yyyy");
  return formatter.format(date);
}

String valuatorSubmissionsCollection() {
  if (kDebugMode) {
    return 'valuatorSumbissions';
  } else {
    return 'valuatorSumbissionsTest';
  }
}

// <EMAIL>
// nishu@1989
customDialogWithActions(BuildContext context, String header_title,
    String description, List<Widget> actionButtons) {
  if (actionButtons.length == 0) {
    actionButtons = [
      ui_button(context, 'OK', () async {
        Navigator.of(context).pop();
      }, 'primary', 'md', null)
    ];
  }

  showDialog<void>(
    context: context,
    barrierDismissible: false, // user must tap button!
    builder: (BuildContext context) {
      return AlertDialog(
        content: Builder(
          builder: (context) {
            return BaseNewarcPopup(
              title: header_title,
              column: Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color.fromARGB(255, 73, 73, 73),
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
              ),
            );
          },
        ),
      );
    },
  );
}

String kappafyPrice(double priceInThousands) {
  try {
    return '€' + (priceInThousands / 100).toStringAsFixed(0) + "k";
  } catch (e) {
    return "error";
  }
}

String formatPrice(double priceInThousands, {int digits = 0}) {
  try {
    NumberFormat formatter = NumberFormat.decimalPattern("it_IT");

    String formattedNumber = formatter.format(priceInThousands);
    return '€' + formattedNumber; //+ priceInThousands.toStringAsFixed(digits);
  } catch (e) {
    return "error";
  }
}

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');
}

bool isToday(int millisecondsSinceEpoch) {
  DateTime givenDate = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  DateTime now = DateTime.now();

  return givenDate.year == now.year &&
         givenDate.month == now.month &&
         givenDate.day == now.day;
}

extension EmailValidator on String {
  bool isValidEmail() {
    return RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(this);
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

String capitalize(String value) {
  if (value.trim().isEmpty) return "";
  return "${value[0].toUpperCase()}${value.substring(1).toLowerCase()}";
}

bool isNumber(dynamic value) {
  return value is num || (value is String && double.tryParse(value) != null);
  try {
    if (value is num) {
      return true;
    } else if (value is String && double.tryParse(value) != null) {
      return true;
    } else if (value is String) {
      value = value.replaceAll('.', '');
      if (double.tryParse(value) != null) return true;
      if (value.endsWith(',')) {
        if (double.tryParse(value.replaceAll(',', '.0')) != null) return true;
      } else {
        if (double.tryParse(value.replaceAll(',', '.')) != null) return true;
      }
      return false;
    } else {
      return false;
    }
  } catch (e) {
    print("VALIDA");

    print(value);
    return false;
  }

  //return value is num || (value is String && double.tryParse(value) != null);
}

String generateRandomString(int len) {
  var r = Random();
  const _chars =
      'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  return List.generate(len, (index) => _chars[r.nextInt(_chars.length)]).join();
}

String convertToCamelCase(String str) {
  // Split the input string by spaces
  List<String> words = str.split(' ');

  // Convert the first word to lowercase
  String camelCaseString = words[0].toLowerCase();

  // Convert the first letter of each subsequent word to uppercase and concatenate
  for (int i = 1; i < words.length; i++) {
    String word = words[i];
    camelCaseString += word[0].toUpperCase() + word.substring(1).toLowerCase();
  }

  return camelCaseString;
}

String convertFromCamelCase(String str) {
  List<String> words = [];
  StringBuffer currentWord = StringBuffer();

  for (int i = 0; i < str.length; i++) {
    if (i > 0 &&
        str[i].toUpperCase() == str[i] &&
        str[i].toLowerCase() != str[i]) {
      // Found an uppercase letter indicating the start of a new word
      words.add(currentWord.toString());
      currentWord = StringBuffer();
    }
    currentWord.write(str[i]);
  }
  // Add the last word
  words.add(currentWord.toString());

  // Join words with a space and capitalize the first word
  String result = words.map((word) {
    return word.toLowerCase();
  }).join(' ');

  // Capitalize the first letter of the first word
  return result[0].toUpperCase() + result.substring(1);
}

double calculateProjectCompletionPercentage(List<ProjectJob>? projectJobs) {
  double total = 1;
  double completedPercentage = 1;
  if (projectJobs != null && projectJobs.isNotEmpty) {
    projectJobs.forEach((projectJob) {

      Map<String, dynamic> activityMap = projectJobsActivities.firstWhere(
          (item) => (item['value']??'').toString().toLowerCase() == (projectJob.activity??'').toString().toLowerCase(),
          orElse: () => {'timelineValue': 0});

      if (activityMap['timelineValue'] != null) {
        total += (activityMap['timelineValue'] ?? 0) as num;
      }
      // total += activityMap['timelineValue'];
      if (projectJob.status == '2 Completati') {
        completedPercentage += (activityMap['timelineValue'] ?? 0) as num;
      }
    });
  }

  if (completedPercentage == 1 && total == 1) {
    return 0;
  } else {
    return completedPercentage / total;
  }
  
  
}

List<String> getJobsInProgress(List<ProjectJob>? projectJobs) {
  List<String> jobsInProgress = [];

  if (projectJobs!.length > 0 ) {
    
    projectJobs.forEach((projectJob) {
      print(projectJob.toMap());
      Map<String, dynamic> activityMap = projectJobsActivities
          .firstWhere((item) => item['value'] == projectJob.activity, orElse: () => {'timelineValue': 0} );

      if (projectJob.status == '1 In corso' ) {
        jobsInProgress.add(activityMap['value']);
      }
    });
  }
  return jobsInProgress;
}

String timestampToUtcDate(int millisecondsSinceEpoch) {
  if (millisecondsSinceEpoch == 0) return '';

  return DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
          .day
          .toString() +
      '/' +
      DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
          .month
          .toString() +
      '/' +
      DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
          .year
          .toString();
}

List<double> getCustomItemsHeights(items, height) {
  final List<double> itemsHeights = [];
  for (int i = 0; i < (items.length * 2) - 1; i++) {
    if (i.isEven) {
      itemsHeights.add(height);
    }
    //Dividers indexes will be the odd indexes
    if (i.isOdd) {
      itemsHeights.add(4);
    }
  }
  return itemsHeights;
}

getProjectActivities() async {

  List _activity = [];
  final FirebaseFirestore _db = FirebaseFirestore.instance;
  QuerySnapshot<Map<String, dynamic>> snapshot = await _db
      .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
      .get();

  if( snapshot.docs.length > 0 ) {
        
    _activity.add({'value': '', 'label': '', 'category': '', 'timelineValue': 0});
    
    for (var i = 0; i < snapshot.docs.length; i++) {
      // List<Map<String, dynamic>> existingActivities = 

      if( snapshot.docs[i].data()['activities'] != null ) {
        (snapshot.docs[i].data()['activities'] as List<dynamic>?)
          ?.map((acti) {
            Map<String, dynamic>.from(acti);
            _activity.add(
            {
              'value': acti['activity'], 
              'label': acti['activity'], 
              'category': snapshot.docs[i].data()['categoryName'], 
              'timelineValue': int.tryParse(acti['timeline'])
            });    
          }).toList();    
      }
      
    }
  }

  return _activity;

}

Map<String, Map<String, dynamic>> deepCopy(Map<String, Map<String, dynamic>> original) {
  return original.map((key, value) => MapEntry(
        key,
        {
          'status': value['status'],
          'children': Map<String, dynamic>.from(value['children']),
        },
      ));
}

Map<String, dynamic> getDefaultMenuAccessByRole(role) {

  switch (role) {
    case 'finance': case 'master': case 'administration':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;

    case 'renderist':
      
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';
      return appConst.allUserRoleMenuControls[role]!;

    case 'geometra':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';
      return appConst.allUserRoleMenuControls[role]!;
    
    case 'media_creator':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'hide';

      return appConst.allUserRoleMenuControls[role]!;
    
    case 'scouter':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;
    
    case 'renovator':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;
    
    case 'administration':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;
      
      
    default:
      return appConst.allUserRoleMenuControls[role]!;
  }

}

Map<String, dynamic> getDefaultProjectTabByRole(role) {

  switch (role) {
    
    case 'renovator':
      
      return {
        "generali": {
          'status': 'show',
          'children': {
            'Dashboard': 'show',
            'Immobile': 'show',
            'Team Newarc': 'show',
          }
        },
        "acquisizione/rivendita": {
          'status': 'show',
          'children': {
            'Agenzia': 'show',
            'Annuncio': 'show'
          }
        },
        "ristrutturazione": {
          'status': 'show',
          'children': {
            'Gestisci Lavori': 'show',
            'Gestisci Materiali': 'show',
            'Aggiornamenti cantiere': 'show',
          }
        },
        "economics": {
          'status': 'hide',
          'children': {
            'Pagamenti': 'hide',
            'Conto Economico': 'hide'
          }
        },
        "file e documenti": {
          'status': 'show',
          'children': {
            'Atti e Contratti': 'show',
            'Progettazione': 'show',
            'Impiantistica': 'show',
            'Planimetrie': 'show',
            'Render': 'show',
            'Tour virtuale': 'show',
            'Pratiche Edilizie': 'show',
            'Brochure': 'show',
            'Foto': 'show',
            'Video': 'show'
          }
        }
      };

    case 'renderist' : case 'media_creator':
      return {
        "generali": {
          'status': 'hide',
          'children': {
            'Dashboard': 'hide',
            'Immobile': 'hide',
            'Team Newarc': 'hide',
          }
        },
        "acquisizione/rivendita": {
          'status': 'hide',
          'children': {
            'Agenzia': 'hide',
            'Annuncio': 'hide'
          }
        },
        "ristrutturazione": {
          'status': 'hide',
          'children': {
            'Gestisci Lavori': 'hide',
            'Gestisci Materiali': 'hide',
            'Aggiornamenti cantiere': 'hide',
          }
        },
        "economics": {
          'status': 'hide',
          'children': {
            'Pagamenti': 'hide',
            'Conto Economico': 'hide'
          }
        },
        "file e documenti": {
          'status': 'show',
          'children': {
            'Atti e Contratti': 'show',
            'Progettazione': 'show',
            'Impiantistica': 'show',
            'Planimetrie': 'show',
            'Render': 'show',
            'Tour virtuale': 'show',
            'Pratiche Edilizie': 'show',
            'Brochure': 'show',
            'Foto': 'show',
            'Video': 'show'
          }
        }
      };
    
    
    default:
      return {
        "generali": {
          'status': 'show',
          'children': {
            'Dashboard': 'show',
            'Immobile': 'show',
            'Team Newarc': 'show',
          }
        },
        "acquisizione/rivendita": {
          'status': 'show',
          'children': {
            'Agenzia': 'show',
            'Annuncio': 'show'
          }
        },
        "ristrutturazione": {
          'status': 'show',
          'children': {
            'Gestisci Lavori': 'show',
            'Gestisci Materiali': 'show',
            'Aggiornamenti cantiere': 'show',
          }
        },
        "economics": {
          'status': 'show',
          'children': {
            'Pagamenti': 'show',
            'Conto Economico': 'show'
          }
        },
        "file e documenti": {
          'status': 'show',
          'children': {
            'Atti e Contratti': 'show',
            'Progettazione': 'show',
            'Impiantistica': 'show',
            'Planimetrie': 'show',
            'Render': 'show',
            'Tour virtuale': 'show',
            'Pratiche Edilizie': 'show',
            'Brochure': 'show',
            'Foto': 'show',
            'Video': 'show'
          }
        }
      };
  }

}

final Map<String, List<String>> pagamentoToActivityMap = {
  "Ristrutturazione": ["C - Lavori interni"],
  "Pratiche": ["B - Pratiche"],
  "Progettazione": ["A - Progettazione"],
  "Forniture": ["M - Forniture"],
  "Arredi": ["A - Arredi"],
  "Porte Interne": ["P - Porte"],
  "Serramenti": ["S - Serramenti"],
};

final Map<String, bool> renovationCategoryMap = {
  "Completo": false,
  "Ristrutturazione": false,
  "Pratiche": false,
  "Progettazione": false,
  "Forniture": false,
  "Arredi": false,
  "Porte Interne": false,
  "Serramenti": false,
};

int roundToNearestThousand(double value) {
  return (value / 1000).round() * 1000;
}

Widget externalEspositionButton(String label, ImmaginaProject project, context, {stateSetter= null}) {
  return Padding(
    padding: const EdgeInsets.only(right: 8.0),
    child: OutlinedButton(
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        side: BorderSide(
            color: project.externalEsposition.contains(label)? Colors.transparent : Color(0xffdbdbdb)
        ),
        backgroundColor: project.externalEsposition.contains(label)? Theme.of(context).primaryColor : Colors.transparent,
      ),
      onPressed: stateSetter == null 
      ? null
      : () {
        if (project.externalEsposition.contains(label)) {
          stateSetter(() {
            project.externalEsposition.removeWhere((item) => item == label);
          });
        } else {
          stateSetter(() {
            project.externalEsposition.add(label);
          });
        }
      },
      child: Text(
        label,
        style: TextStyle(
          color: project.externalEsposition.contains(label) ? Theme.of(context).unselectedWidgetColor : Theme.of(context).primaryColorDark,
          fontSize: 14,
          fontFamily: 'Raleway-600',
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
  );
}

Future<bool> getAdStatus( String adId ) async {

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  DocumentSnapshot<Map<String, dynamic>> data = await _db.collection(appConfig.COLLECT_NEWARC_HOME)
  .doc(adId)
  .get();

  if( data.exists ) {
    Property ad = Property.fromDocument(data );
    return ad.isActive!;
  } else {
    return false;
  }
  
} 

Future<bool> setStatus(String adId, bool status ) async {

  try {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db.collection(appConfig.COLLECT_NEWARC_HOME)
    .doc(adId)
    .update({
      'isActive':status
    });

    return true;

  } catch (e) {
    return false;
  }
}

fetchAgencyDetails( agencyId ) async {

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  DocumentSnapshot<Map<String, dynamic>> dbQuery = await _db.collection(appConfig.COLLECT_AGENCIES)
      .doc(agencyId)
      .get();

  Map agencyData = {
    'agencyId': '',
    'agencyProfilePicture': ''
  };

  if( dbQuery.exists ) {

    agencyData['agencyId'] = dbQuery.id;
    
    QuerySnapshot<Map<String, dynamic>> dbQueryUser = await _db.collection(appConfig.COLLECT_USERS)
        .where('agencyId', isEqualTo: dbQuery.id )
        .get();

    if( dbQueryUser.docs.length > 0 ) {
      
      AgencyUser agencyUser = AgencyUser.fromDocument(dbQueryUser.docs[0].data(), dbQueryUser.docs[0].id);

      if( agencyUser.profilePicture != null ) {
        String filename = agencyUser.profilePicture.toString().trim();
        // filename = imageElement['filename'];
        // console.log(filename);
        int dotIndex = filename.indexOf('.');
        // if (dotIndex === -1) return filename + '_thumbnail'; // no extension case

        String name = filename.substring(0, dotIndex);
        String extension = filename.substring(dotIndex);
        String agencyFileName = '${name}_thumbnail${extension}';

        String agencyImage = await agencyProfileUrl(dbQuery.id, agencyFileName );
        // agency['user']['profilePicture'] = agencyImage;
        agencyData['agencyProfilePicture'] = agencyImage;
      }
      
    }
  }
  
  return agencyData;
  

}

fetchProfessionalDetails( professionalId ) async {

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  DocumentSnapshot<Map<String, dynamic>> dbQuery = await _db.collection(appConfig.COLLECT_PROFESSIONALS)
      .doc(professionalId)
      .get();

  Map agencyData = {
    'agencyId': '',
    'agencyProfilePicture': ''
  };

  if( dbQuery.exists ) {

    agencyData['agencyId'] = dbQuery.id;
    
    QuerySnapshot<Map<String, dynamic>> dbQueryUser = await _db.collection(appConfig.COLLECT_USERS)
        .where('professionalId', isEqualTo: dbQuery.id )
        .get();

    if( dbQueryUser.docs.length > 0 ) {
      
      NewarcUser agencyUser = NewarcUser.fromDocument(dbQueryUser.docs[0].data(), dbQueryUser.docs[0].id);

      if( agencyUser.profilePicture != null ) {
        String filename = agencyUser.profilePicture.toString().trim();
        // filename = imageElement['filename'];
        // console.log(filename);
        int dotIndex = filename.indexOf('.');
        // if (dotIndex === -1) return filename + '_thumbnail'; // no extension case

        String name = filename.substring(0, dotIndex);
        String extension = filename.substring(dotIndex);
        // String agencyFileName = '${name}_thumbnail${extension}';
        String agencyFileName = '${name}${extension}';

        String agencyImage = await professionalProfileUrl(dbQueryUser.docs[0].id, agencyFileName );
        // agency['user']['profilePicture'] = agencyImage;
        agencyData['agencyProfilePicture'] = agencyImage;

        
      }
      
    }
  }
  
  return agencyData;
  

}

fetchNewarcProjectDetails( String mainAdId ) async {

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  QuerySnapshot<Map<String, dynamic>> dbQuery = await _db.collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .where('propertyId', isEqualTo: mainAdId )
        .get();

  Map agencyData = {};
  if ( dbQuery.docs.length > 0 ) {
    
    NewarcProject newarcProject = NewarcProject.fromDocument(dbQuery.docs[0].data(), dbQuery.docs[0].id);
    if( newarcProject.assignedAgency!['agencyId'] != '' ) {
      return await fetchAgencyDetails( newarcProject.assignedAgency!['agencyId'] );
    }

  }
  
  return {
    'agencyId': '',
    'agencyProfilePicture': ''
  };

  
}

fetchImmaginaProjectDetails( String mainAdId ) async {

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  QuerySnapshot<Map<String, dynamic>> dbQuery = await _db.collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
        .where('propertyId', isEqualTo: mainAdId )
        .get();

  if ( dbQuery.docs.length > 0 && dbQuery.docs[0].exists ) {
    ImmaginaProject immaginaProject = ImmaginaProject.fromDocument(dbQuery.docs[0].data(), dbQuery.docs[0].id);
    
    if( immaginaProject.agencyId != '' ) {          
      return await fetchAgencyDetails(immaginaProject.agencyId);
    } else if( immaginaProject.professionalId != '' )  {
      return await fetchProfessionalDetails(immaginaProject.professionalId);
    }

  }
  
  return {
    'agencyId': '',
    'agencyProfilePicture': ''
  };

}

updateWebsiteAd( String mainAdId ) async {

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  DocumentSnapshot<Map<String, dynamic>> adQuery = await _db.collection(appConfig.COLLECT_NEWARC_HOME)
      .doc(mainAdId)
      .get();

  
  if( !adQuery.exists ) return false;

  Map agencyData = {
    'agencyId': '',
    'agencyProfilePicture': ''
  };

  Property adData = Property.fromDocument(adQuery);
  try {

    if( adData.projectType == 'Newarc' ) {
      agencyData = await fetchNewarcProjectDetails(adData.firebaseId!);
    } else if( adData.projectType == 'Immagina' || adData.projectType == 'Immagina for Professionals'  ) {
      agencyData = await fetchImmaginaProjectDetails(adData.firebaseId!);
    }

    QuerySnapshot<Map<String, dynamic>> projectQuery = await _db.collection(appConfig.COLLECT_WEBSITE_ADS)
        .where('newarcHomeId', isEqualTo: adData.firebaseId )
        .get();

    List<WebsiteAdChild> children = [];
    List<String> _images = []; 

    if( adData.projectType == 'Immagina for Professionals' && adData.cutsCount.toString().toLowerCase() == 'multiple' ) {

      if( adData.buildingPictures!.length > 0 ) {
        for (var i = 0; i < adData.buildingPictures!.length; i++) {
          if( adData.buildingPictures![i]['isEnabledForWebsite'] ) {
            
            String filename = adData.buildingPictures![i]['filename'];
            int dotIndex = filename.indexOf('.');

            String name = filename.substring(0, dotIndex);
            String extension = filename.substring(dotIndex);
            String thumbnailFilename = '${name}_thumbnail${extension}';

            String _image = await printUrl(adData.buildingPictures![i]['location'],'' ,thumbnailFilename );
            _images.add(_image);

          }
        }
      }

      // If the ad has children
      if( adData.children!.length > 0 ) {
        for (var i = 0; i < adData.children!.length; i++) {
          QuerySnapshot<Map<String, dynamic>> projectQuery1 = await _db.collection(appConfig.COLLECT_WEBSITE_ADS)
          .where('newarcHomeId', isEqualTo: adData.children![i] )
          .get();

          if( projectQuery1.docs.length > 0 ) {
            Property childAdData = Property.fromDocument(projectQuery1.docs.first );
            WebsiteAdChild websiteAdChild = WebsiteAdChild(
              newarcHomeId: childAdData.firebaseId,
              isArchived: childAdData.isArchived,
              isActive: childAdData.isActive,
              type: childAdData.type,
              floorArea: double.tryParse(childAdData.mq.toString()),
              roomCount: double.tryParse(childAdData.locals.toString()),
              floors: childAdData.floors,
              baths: double.tryParse(childAdData.baths.toString()),
              price: double.tryParse(childAdData.styles![0].price??''),
              image: ''

            );

            if( childAdData.photoDayTimePaths!.length > 0 ) {

              for (var i = 0; i < childAdData.photoDayTimePaths!.length; i++) {
                if( childAdData.photoDayTimePaths![i]['isEnabledForWebsite'] ) {

                  String filename = childAdData.photoDayTimePaths![i]['filename'];
                  int dotIndex = filename.indexOf('.');

                  String name = filename.substring(0, dotIndex);
                  String extension = filename.substring(dotIndex);
                  String thumbnailFilename = '${name}_thumbnail${extension}';

                  String imageUrl = await printUrl(childAdData.photoDayTimePaths![i]['location'] ,'', thumbnailFilename);
                  websiteAdChild.image = imageUrl;
                  break;
                }
                
              }
            }
            children.add(websiteAdChild);
          }

        }
      }
      
    } else {
      if( adData.photoDayTimePaths!.length > 0 ) {
        for (var i = 0; i < adData.photoDayTimePaths!.length; i++) {
          if( adData.photoDayTimePaths![i]['isEnabledForWebsite'] ) {
            
            String filename = adData.photoDayTimePaths![i]['filename'];
            int dotIndex = filename.indexOf('.');

            String name = filename.substring(0, dotIndex);
            String extension = filename.substring(dotIndex);
            String thumbnailFilename = '${name}_thumbnail${extension}';

            String _image = await printUrl(adData.photoDayTimePaths![i]['location'] ,'', thumbnailFilename);
            _images.add(_image);
          }
        }
      }
    }

    WebsiteAd websiteAd = WebsiteAd(
      newarcHomeId: adData.firebaseId,
      isActive: adData.isActive,
      isArchived: adData.isArchived,
      publicationDate: adData.publicationDate,
      title: adData.addressInfo!.toShortAddress(),
      floorArea: double.tryParse(adData.mq.toString()),
      roomCount: double.tryParse(adData.locals.toString()),
      baths: double.tryParse(adData.baths.toString()),
      floors: adData.floors,
      price: double.tryParse(adData.styles![0].price??''),
      projectType: adData.projectType,
      status: adData.projectStatus,
      images: _images,
      addressInfo: adData.addressInfo,
      children: children,
      agencyId: agencyData['agencyId'],
      agencyLogoImage: agencyData['agencyProfilePicture'],
      zone: adData.zone,
      publicStatus: adData.publicStatus
    );

    if( projectQuery.docs.length > 0 ) {

      await _db.collection(appConfig.COLLECT_WEBSITE_ADS)
        .doc(projectQuery.docs.first.id )
        .update(websiteAd.toMap());

    } else {
      await _db.collection(appConfig.COLLECT_WEBSITE_ADS)
        .add(websiteAd.toMap());

    }




  } catch (e,s ) {
    print({'updateWebsiteAd', e,s});
  }
  

}


Future<List<Uint8List>> convertPdfBytesInFlutter(Uint8List pdfBytes) async {
  final jsPromise = js_util.callMethod(
    html.window,
    'convertPdfBytesToJpegArrays',
    [pdfBytes, 0.85, 1.0],
  );

  final resultList = await js_util.promiseToFuture<List>(jsPromise);
  return resultList.map((base64) => base64Decode(base64)).toList();
}

addAddressToUser( RenovationContactAddress address, Map updateReference ) async {

  if (address.id == '') {
    DocumentReference<Map<String, dynamic>> addedAddress =  await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
    .add(address.toMap());

    updateDocument( updateReference['collection'] , updateReference['id'], updateReference['data']);
    
    
  } else {
    
    await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
        .doc(address.id)
        .update(address.toMap());
  }
  
}

Future<RenovationContactAddress> getRenovationContactAddress( String addressId ) async {
    
  DocumentSnapshot<Map<String, dynamic>> address = await FirebaseFirestore.instance
    .collection('renovationContactAddress')
    .doc(addressId)
    .get();

  if( address.exists ) {
    RenovationContactAddress responseAddress = RenovationContactAddress.fromDocument(address.data()!, address.id);
    return responseAddress;

  }

  return RenovationContactAddress.empty();

}