import 'dart:developer';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';

import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/pages/agency/suggested_contacts/suggested_contacts_controller.dart';
import 'package:newarc_platform/pages/agency/suggested_contacts/suggested_contacts_data_source.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;


class SuggestedContactsView extends StatefulWidget {
  final responsive;
  final String? agencyId;

  SuggestedContactsView({
    Key? key, 
    required this.responsive, 
    this.agencyId,
    }) : super(key: key);

  @override
  State<SuggestedContactsView> createState() =>
      _SuggestedContactsViewState();
}

class _SuggestedContactsViewState extends State<SuggestedContactsView> {
  
  final controller = Get.put<SuggestedContactsController>(SuggestedContactsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.stateFilterController.text = '';
    initialFetchSuggestedContacts(force: true);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initialFetchSuggestedContacts({bool force = false}) async {

    if (controller.contacts.isNotEmpty && !force) return;

    setState(() {
      controller.contacts = [];
      controller.loading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_CONTACTS);

      if (controller.stateFilterController.text != '') {
        collectionSnapshotQuery = collectionSnapshotQuery.where('suggestionStatus',
            isEqualTo: controller.stateFilterController.text);
      }
      collectionSnapshotQuery = collectionSnapshotQuery
        .where('isArchived', isEqualTo: false)
        .where('isSuggestedContact', isEqualTo: true)
        .where('agencyId', isEqualTo: widget.agencyId)
        .orderBy('created', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();

      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        RenovationContact tmpSuggCont = RenovationContact.fromDocument(
            collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);
        controller.contacts.add(tmpSuggCont);
      }
      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('Following error', error: e, stackTrace: s);
    }
  }

  Future<void> showAddSuggestedContactPopup() async {
    controller.clearPopupcontrollers();
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, setState) {
            return Center(
                child: BaseNewarcPopup(
                    title: "Nuova segnalazione",
                    buttonText: "Segnala contatto",
                    formErrorMessage: controller.formErrorMessage,
                    onPressed: () async {
                      setState(() {
                        controller.formProgressMessage = 'Saving';
                        controller.formErrorMessage = ['Salvataggio...'];
                        controller.loading = true;
                      });

                      try {
                        Map<String, dynamic> contactData = {
                          'name': controller.contactNameController.text,
                          'surname': controller.contactSurnameController.text,
                          'email': controller.contactEmailController.text,
                          'phone': controller.contactPhoneController.text,
                        };

                        if (!controller.addressInfo.isValidAddress()) {
                          setState(() {
                            controller.formProgressMessage = 'Indirizzo non valido';
                            controller.formErrorMessage = ['Indirizzo non valido'];
                            controller.loading = false;
                          });
                          return false;
                        }
                        BasePersonInfo contactInfo = BasePersonInfo.fromMap(contactData);
                        RenovationContact suggestedContact = RenovationContact.empty();
                        suggestedContact.personInfo = contactInfo;

                        RenovationContactAddress renovationContactAddress = RenovationContactAddress({
                          'addressInfo': controller.addressInfo,
                          'created': Timestamp.now().millisecondsSinceEpoch,
                          'isArchived': false
                        });

                        DocumentReference<Map<String, dynamic>> addedAddress = await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                        .add(renovationContactAddress.toMap());

                        suggestedContact.addressInfo!.add(addedAddress.id);
                        suggestedContact.agencyId = widget.agencyId;
                        suggestedContact.suggestionStatus = suggestionStatus[suggestionStatus.indexWhere((sta) => sta['label'] == 'Segnalato')]['value'];
                        suggestedContact.isSuggestedContact = true;
                        suggestedContact.isRequestingQuotation = false;

                        await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                        .add(suggestedContact.toMap());

                        await initialFetchSuggestedContacts(force: true);
                        return true;
                      } catch (e, s) {
                        setState(() {
                          controller.formProgressMessage = 'Error';
                          controller.formErrorMessage = ['Errore generico'];
                          controller.loading = false;
                        });
                        print({e,s});
                        return false;
                      }
                      // return true;
                    },
                    // To make custom popup using the bluprint of  RenovationContactPopup
                    column: Container(
                      width: 400,
                      height: 400,
                      child: controller.loading
                        ? Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),)
                        : ListView(
                        children: [
                          AddressSearchBar(
                            label: "Indirizzo Ristrutturazione", 
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Obbligatorio';
                              }
                            },
                            onPlaceSelected: (selectedPlace){
                              debugPrint('Selected place: \n$selectedPlace');
                              BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                              if (selectedAddress.isValidAddress()){
                                controller.addressInfo = selectedAddress;
                              } else {
                                controller.addressInfo = BaseAddressInfo.empty();
                              }
                            }
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "Nome",
                                validator: (value) {
                                  if (value == '') {
                                    return 'Obbligatorio';
                                  }
                                  return null;
                                },
                                controller: controller.contactNameController,
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "Cognome",
                                validator: (value) {
                                  if (value == '') {
                                    return 'Obbligatorio';
                                  }
                                  return null;
                                },
                                controller: controller.contactSurnameController,
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "E-Mail",
                                controller: controller.contactEmailController,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Obbligatorio';
                                  }
                                  final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                  if (!emailRegex.hasMatch(value)) {
                                    return 'Inserisci un indirizzo email valido';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "Telefono",
                                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),],
                                controller: controller.contactPhoneController,
                                validator: (value) {
                                  if (value == '') {
                                    return 'Obbligatorio';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                        ],
                      ),
                    )
                  )
                );
          }
        );
        }
      );
  }

  List<Map> suggestionStatus = [
    {
      'value': 'segnalato',
      'label': 'Segnalato',
      'bgColor': Color(0xffD4D4D4),
      'textColor': Colors.black
    },
    {
      'value': 'acquisito',
      'label': 'Acquisito',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.white
    },
    {
      'value': 'non-acquisito',
      'label': 'Non acquisito',
      'bgColor': Color(0xffDD0000),
      'textColor': Colors.white
    },
  ];

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: 'Segnala ristrutturazione',
                fontSize: 19,
                fontWeight: '700',
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          showAddSuggestedContactPopup();
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Nuova segnalazione",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              controller.loading
                  ? Container()
                  : NarFilter(
                showSearchInput: false,
                textEditingControllers: [
                  controller.stateFilterController,
                ],
                selectedFilters: [
                  controller.stateSelectedFilter,
                ],
                filterFields: [
                  {
                    'Preventive State': NarImageSelectBoxWidget(
                      options: suggestionStatus,
                      onChanged: (dynamic val) {
                        controller.stateSelectedFilter = val['label'];
                        setState(() {});
                      },
                      controller: controller.stateFilterController,
                    ),
                  },
                ],
                onSubmit: () async {
                  await initialFetchSuggestedContacts(force: true);
                },
                onReset: () async {
                  controller.clearFilter();
                  await initialFetchSuggestedContacts(force: true);
                },
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loading ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          columns: [
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Indirizzo',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Nome e Cognome',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Telefono',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Email',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Stato',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Commissione',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Data segnalazione',
                              ),
                            ),
                          ],
                          source: SuggestedContactsDataSource(
                            initialFetchContacts: initialFetchSuggestedContacts,
                            contacts: controller.contacts,
                            status: suggestionStatus,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loading)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      // dataTablePagination(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
