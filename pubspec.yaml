name: newarc_platform
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: # Add this line
    sdk: flutter   

  google_fonts: ^4.0.2
  cupertino_icons: ^1.0.2
  google_maps_flutter_web: ^0.5.8
  csv: ^6.0.0
  provider: ^6.1.2
  http: ^0.13.6
  collection: ^1.15.0
  cloud_functions: ^4.0.2
  firebase_core: ^2.1.0
  async: ^2.8.1
  dropdown_button2: ^2.3.9
  cloud_firestore: ^4.0.2
  firebase_auth: ^4.0.2
  dropdown_below: ^1.0.3
  adaptive_scrollbar: ^2.1.2
  url_launcher: ^6.1.3
  data_table_2: ^2.6.0
  flutterfire_ui: ^0.4.3+20
  mask_text_input_formatter: ^2.4.0
  url_strategy: ^0.2.0
  flutter_svg: ^1.1.6
  clipboard: ^0.1.3
  fluttertoast: ^8.1.2
  image_picker: ^1.1.2
  firebase_storage: ^11.0.14
  flutter_google_places_hoc081098: ^1.0.0-nullsafety.5
  google_api_headers: ^1.1.1
  google_maps_webservice: ^0.0.20-nullsafety.5
  google_maps_flutter: ^2.1.1
  geocoding: ^3.0.0
  fl_chart: ^0.68.0
  image_compression_flutter: ^1.0.4
  path_provider: ^2.1.3
  win32: 5.5.1
  file_picker: 8.0.6
  syncfusion_flutter_pdfviewer: 20.4.54
  video_player: 2.9.1
  pdf: ^3.11.0
  flutter_image_stack: ^0.0.7
  country_picker: ^2.0.27
  get: ^4.6.6
  intl:
  js: ^0.6.5
  qr_flutter: ^4.1.0
  flutter_colorpicker: ^1.1.0
  mime: ^1.0.6





dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo.png
    - assets/logo-agenzie-white.png
    - assets/map_style.json
    - assets/various/group_green.png
    - assets/various/group_grey.png
    - assets/icon.png
    - assets/icon-dark.png
    - assets/icons/mole-antonelliana.png
    - assets/icons/agency.png
    - assets/icons/newhouse.svg
    - assets/icons/map_icons/green.png
    - assets/icons/map_icons/orange.png
    - assets/icons/map_icons/yellow.png
    - assets/icons/building.png
    - assets/icons/locali.png
    - assets/icons/mq.png
    - assets/icons/stairs.png
    - assets/icons/dashboard.png
    - assets/icons/contacts.png
    - assets/icons/operations.png
    - assets/icons/propose.png
    - assets/icons/acquisto.png
    - assets/icons/vendita.png
    - assets/verify_account_icon.svg
    - assets/icons/account.svg
    - assets/icons/plane.svg
    - assets/icons/account.svg
    - assets/icons/milan.svg
    - assets/icons/rome.svg
    - assets/icons/agenzie.svg
    - assets/icons/edit.svg
    - assets/icons/comment.svg
    - assets/home-agencies.png
    - assets/agency-black-logo.svg
    - assets/icons/trash.svg
    - assets/icons/close-popup.svg
    - assets/icons/arrow_left.svg
    - assets/icons/arrow_up.svg
    - assets/icons/arrow_down.svg
    - assets/icons/arrow_left.png
    - assets/icons/arrow_up.png
    - assets/icons/arrow_down.png
    - assets/close.png
    - assets/check.png
    - assets/icons/grey-paper-plane.png
    - assets/icons/white-paper-plane.png
    - assets/icons/message-bubble.png
    - assets/icons/user-icon.png
    - assets/icons/account_placeholder.png
    - assets/icons/calendar.png
    - assets/icons/calendar.svg
    - assets/various/iscrizione_programma_agenzie.jpg
    - assets/icons/file.png
    - assets/icons/pdf.png
    - assets/icons/video.png
    - assets/icons/trash-process.png
    - assets/icons/edit-process.png
    - assets/icons/edit.png
    - assets/icons/drag.png
    - assets/icons/comment-bubble.png
    - assets/icons/camera.png
    - assets/icons/download.png
    - assets/icons/document.png
    - assets/icons/download_brochure.svg
    - assets/logo_newarc_immagina.png
    - assets/icons/pdf_play_icon.png
    - assets/icons/pdf-house-arrow.png
    - assets/icons/pdf-material-premium.png
    - assets/icons/pdf-material-standard.png
    - assets/icons/pdf-infissi.png
    - assets/icons/pdf-restructure-hammer.png
    - assets/icons/pdf-arrow.png
    - assets/background_last_page.jpg
    - assets/icons/pdf-phone.png
    - assets/icons/pdf-location.png
    - assets/immagina_logo_white.png
    - assets/icons/pdf-icon-area.png
    - assets/icons/pdf-icon-rooms.png
    - assets/icons/pdf-icon-baths.png
    - assets/icons/im-virtual-tour.svg
    - assets/icons/im-pdf.svg
    - assets/icons/im-portali.svg
    - assets/icons/im-plan.svg
    - assets/icons/im-video-render.svg
    - assets/icons/im-render.svg
    - assets/icons/im-photograh.svg
    - assets/icons/closed-eye.svg
    - assets/icons/camera.svg
    - assets/icons/comment-bubble.svg
    - assets/icons/up_down.svg
    - assets/icons/pdf_icon.svg
    - assets/icons/link.svg
    - assets/icons/pdf_icon_character.png
    - assets/icons/pdf_icon_building.png
    - assets/icons/pdf_icon_heat_energy.png
    - assets/icons/pdf_icon_shopping.png
    - assets/icons/pdf_icon_school.png
    - assets/icons/pdf_icon_filled_location.png
    - assets/icons/pdf_icon_stairs.png
    - assets/icons/pdf_icon_bath.png
    - assets/icons/pdf_icon_room.png
    - assets/icons/pdf_icon_mg.png
    - assets/icons/pdf_icon_location.png
    - assets/icons/square-meters.svg
    - assets/icons/rooms.svg
    - assets/icons/bathrooms.svg
    - assets/icons/discount.svg
    - assets/icons/c3-arrow.png
    - assets/icons/photo_template_short.png
    - assets/icons/photo_template_wide.png
    - assets/icons/virtual-tour.svg
    - assets/email-verified.svg
    - .env.production
    - .env.staging
    - assets/
    - assets/icons/
    - assets/various/
    - assets/character/
#    - assets/renovation-quotation-pdf-bg.jpg
#    - assets/rq-pdf-cover-logo.png
#    - assets/cover-2-1.jpg
#    - assets/cover-2-2.jpg
#    - assets/cover-2-3.jpg
#    - assets/cover-2-4.jpg
#    - assets/icons/unlink.png
#    - assets/icons/link-project.png
#    - assets/icons/filter.svg
#    - assets/icons/cash_in.svg
#    - assets/icons/cash_out.svg
#    - assets/icons/delete.svg
#    - assets/icons/arrow_right.svg
#    - assets/icons/search.svg
#    - assets/icons/question_mark.svg
#    - assets/icons/right.svg
#    - assets/icons/wrong.svg
#    - assets/work_logo.svg
#    - assets/logo_work_white.png
#    - assets/icons/
    - assets/renovation-quotation-pdf-bg.jpg
    - assets/renovation-quotation-pdf-bg-new.jpg
    - assets/partners.png
    - assets/rq-pdf-cover-logo.png
    - assets/newarc_immagina.svg
    - assets/cover-2-1.jpg
    - assets/cover-2-2.jpg
    - assets/cover-2-3.jpg
    - assets/cover-2-4.jpg
    - assets/icons/unlink.png
    - assets/icons/link-project.png
    - assets/icons/filter.svg
    - assets/icons/cash_in.svg
    - assets/icons/cash_out.svg
    - assets/icons/delete.svg
    - assets/icons/arrow_right.svg
    - assets/icons/search.svg
    - assets/icons/true.svg
    - assets/icons/question_mark.svg
    - assets/icons/right.svg
    - assets/icons/wrong.svg
    - assets/work_logo.svg
    - assets/work_logo.png
    - assets/icons/valid.png
    - assets/icons/payment-options.png
    - assets/icons/work-duration.png
    - assets/icons/agency-action.svg
    - assets/icons/archive.png
    - assets/icons/restore.png
    - assets/icons/coins.svg
    - assets/icons/hand_shake_with_house.svg
    - assets/icons/hand_shake_blue.svg
    - assets/icons/calculator.svg
    - assets/icons/check.svg
    - assets/icons/copy.svg
    - assets/icons/dot_icon.svg
    - assets/icons/plus.svg
    - assets/immagina-logo-esteso.png
    - assets/immagina-logo-green.png
    - assets/empty_cover_img.png
    - assets/icons/setting.svg
    - assets/icons/logout.svg
    - assets/icons/price_tag.svg
    - assets/icons/cover_classic.png
    - assets/icons/cover_full.png
    - assets/icons/ic_newarc.svg
    - assets/icons/ic_report.svg
    - assets/icons/ic_segnala.svg
    - assets/icons/ic_contatti.svg
    - assets/icons/icon_note.png
    - assets/icons/complete.svg
    - assets/icons/three_dot.svg
    - assets/icons/req_payment.svg
    - assets/icons/completed.svg
    - assets/icons/new_mail.svg
    - assets/icons/confirmed.svg
    - assets/icons/eye.svg
    - assets/icons/mobile.svg
    - assets/icons/cross.svg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Raleway-300
      fonts:
        - asset: assets/fonts/Raleway-Light.ttf
    - family: Raleway-400
      fonts:
        - asset: assets/fonts/Raleway-Regular.ttf
    - family: Raleway-500
      fonts:
        - asset: assets/fonts/Raleway-Medium.ttf
    - family: Raleway-600
      fonts:
        - asset: assets/fonts/Raleway-Medium.ttf
    - family: Raleway-normal
      fonts:
        - asset: assets/fonts/Raleway-Medium.ttf
    - family: Raleway-700
      fonts:
        - asset: assets/fonts/Raleway-Bold.ttf

    #italic 700 bold
    - family: Raleway-700_italic
      fonts:
        - asset: assets/fonts/Raleway-BoldItalic.ttf


    - family: Raleway-800
      fonts:
        - asset: assets/fonts/Raleway-Bold.ttf
    - family: Raleway-bold
      fonts:
        - asset: assets/fonts/Raleway-Bold.ttf
    - family: Raleway-900
      fonts:
        - asset: assets/fonts/Raleway-ExtraBold.ttf
    - family: semi-bold
      fonts:
        - asset: assets/fonts/Raleway-SemiBold.ttf
    - family: light-italic
      fonts:
        - asset: assets/fonts/Raleway-LightItalic.ttf
    - family: Tinos-700
      fonts:
        - asset: assets/fonts/Tinos-Bold.ttf
    - family: Tinos-bold
      fonts:
        - asset: assets/fonts/Tinos-Bold.ttf
    - family: Tinos-400
      fonts:
        - asset: assets/fonts/Tinos-Regular.ttf
    - family: Tinos-500
      fonts:
        - asset: assets/fonts/Tinos-Regular.ttf
    - family: Tinos-Bold-Italic
      fonts:
        - asset: assets/fonts/Tinos-BoldItalic.ttf
      
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
