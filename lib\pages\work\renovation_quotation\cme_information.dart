import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../../classes/renovationQuotation.dart';
import '../../../utils/color_schema.dart';
import '../../../utils/download_computo_metrico_pdf.dart';
import '../../../widget/UI/base_newarc_button.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/checkbox.dart';
import '../../../widget/UI/custom_textformfield.dart';
import '../../../widget/UI/file-picker.dart';
import '../../../widget/UI/form-label.dart';
import '../../../widget/UI/select-box.dart';
import '../../../widget/UI/textarea.dart';

class CMEInformationInsideView extends StatefulWidget {
  final RenovationQuotation? renovationQuotation;
  final Function? updateViewCallback;
  const CMEInformationInsideView({super.key,this.renovationQuotation,this.updateViewCallback});

  @override
  State<CMEInformationInsideView> createState() => _CMEInformationInsideViewState();
}

class _CMEInformationInsideViewState extends State<CMEInformationInsideView> {

  TextEditingController projectDescriptionController = TextEditingController();
  TextEditingController specificationDescriptionController = TextEditingController();

  TextEditingController limitedTrafficZoneController = TextEditingController();
  TextEditingController blueStripesController = TextEditingController();
  TextEditingController drivewayAccessController = TextEditingController();
  TextEditingController possibilityOfFreightElevatorController = TextEditingController();
  TextEditingController possibilityOfRubbleContainerController = TextEditingController();
  TextEditingController elevatorController = TextEditingController();
  TextEditingController walkableSquareMetersController = TextEditingController();
  TextEditingController roomHeightController = TextEditingController();
  TextEditingController virtualTourController = TextEditingController();
  List<String> formMessages = [];

  List<String> executiveGraphicDrawingTypeList = ["Demolizioni","Tramezzi interni","Pavimentazioni","Tinteggiature","Porte interne","Dettagli WC","Controsoffitti","Impianto elettrico","Altro"];

  @override
  void initState() {
    super.initState();
    initializeCMEInfo();
    virtualTourController.addListener(() {
      setState(() {});
    });
  }

  void initializeCMEInfo(){
    projectDescriptionController.text = widget.renovationQuotation?.cmeInformation?.projectDescription ?? "";
    walkableSquareMetersController.text = widget.renovationQuotation?.cmeInformation?.walkableSquareMeters != null ? widget.renovationQuotation?.cmeInformation?.walkableSquareMeters.toString() ?? "" : "";
    roomHeightController.text = widget.renovationQuotation?.cmeInformation?.roomHeight != null ? widget.renovationQuotation?.cmeInformation?.roomHeight.toString() ?? "" : "";
    possibilityOfRubbleContainerController.text = widget.renovationQuotation?.cmeInformation?.possibilityOfRubbleContainer ?? "";
    possibilityOfFreightElevatorController.text = widget.renovationQuotation?.cmeInformation?.possibilityOfFreightElevator ?? "";
    limitedTrafficZoneController.text = widget.renovationQuotation?.cmeInformation?.limitedTrafficZone ?? "";
    elevatorController.text = widget.renovationQuotation?.cmeInformation?.elevator ?? "";
    drivewayAccessController.text = widget.renovationQuotation?.cmeInformation?.drivewayAccess ?? "";
    blueStripesController.text = widget.renovationQuotation?.cmeInformation?.blueStripes ?? "";
    virtualTourController.text = widget.renovationQuotation?.cmeInformation?.virtualTourURL ?? "";
    specificationDescriptionController.text = widget.renovationQuotation?.cmeInformation?.specificationDescription ?? "";
  }

  @override
  void dispose() {
    super.dispose();
    virtualTourController.dispose();
    projectDescriptionController.dispose();
    limitedTrafficZoneController.dispose();
    blueStripesController.dispose();
    drivewayAccessController.dispose();
    possibilityOfFreightElevatorController.dispose();
    possibilityOfRubbleContainerController.dispose();
    elevatorController.dispose();
    walkableSquareMetersController.dispose();
    roomHeightController.dispose();
    virtualTourController.dispose();
    specificationDescriptionController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Color(0XFFF9F9F9),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  hoverColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  onPressed: () {
                      widget.updateViewCallback!('renovation-quotation');
                  },
                  icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                      height: 20, color: Colors.black),
                ),
                SizedBox(
                  width: 10,
                ),
                NarFormLabelWidget(
                  label: widget.renovationQuotation!.renovationContactAddress!.addressInfo!.toShortAddress() ,
                  fontSize: 22,
                  fontWeight: 'bold',
                  textColor: Colors.black,
                ),
              ]
          ),
          SizedBox(height: 10),
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(13),
                  border: Border.all(color: Color(0xFFB7B7B7),width: 1)
                ),
                padding: EdgeInsets.symmetric(vertical: 22,horizontal: 26),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: "Informazioni aggiuntive per CME",
                      fontSize: 20,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                    SizedBox(height: 27,),
                    //------ Project Description
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 26,vertical: 18),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Descrizione del progetto',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 12),
                          NarTextareaWidget(
                            hintText: "",
                            maxLines: 8,
                            minLines: 8,
                            actionKeyboard: TextInputAction.done,
                            controller: projectDescriptionController,
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 27,),
                    //------ Informazioni utili
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 26,vertical: 18),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Informazioni utili',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 12),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: NarSelectBoxWidget(
                                  label: "ZTL",
                                  options: ["No","Sì"],
                                  controller: limitedTrafficZoneController,
                                ),
                              ),
                              SizedBox(width: 17),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: NarSelectBoxWidget(
                                  label: "Striscie blu",
                                  options: ["No","Sì"],
                                  controller: blueStripesController,
                                ),
                              ),
                              SizedBox(width: 17),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: NarSelectBoxWidget(
                                  label: "Accesso carrabile",
                                  options: ["No","Sì"],
                                  controller: drivewayAccessController,
                                ),
                              ),
                              SizedBox(width: 17),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: NarSelectBoxWidget(
                                  label: "Possibilità montacarichi",
                                  options: ["No","Sì"],
                                  controller: possibilityOfFreightElevatorController,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: NarSelectBoxWidget(
                                  label: "Possibilità cassone macerie",
                                  options: ["No","Sì","Da verificare"],
                                  controller: possibilityOfRubbleContainerController,
                                ),
                              ),
                              SizedBox(width: 17),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: CustomTextFormField(
                                  isExpanded: false,
                                  label: 'Metri quadri calpestabili',
                                  hintText: "",
                                  isMoney: true,
                                  isShowPrefillMoneyIcon: false,
                                  suffixIcon: Container(
                                    height: 20,
                                    width: 20,
                                    alignment: Alignment.center,
                                    child: NarFormLabelWidget(
                                      label: 'mq',
                                      fontSize: 14,
                                      fontWeight: '600',
                                      textColor: Color(0xFF959595),
                                    ),
                                  ),
                                  controller: walkableSquareMetersController,
                                ),
                              ),
                              SizedBox(width: 17),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: CustomTextFormField(
                                  isExpanded: false,
                                  label: 'Altezza dei locali',
                                  hintText: "",
                                  isMoney: true,
                                  isShowPrefillMoneyIcon: false,
                                  suffixIcon: Container(
                                    height: 20,
                                    width: 20,
                                    alignment: Alignment.center,
                                    child: NarFormLabelWidget(
                                      label: 'cm',
                                      fontSize: 14,
                                      fontWeight: '600',
                                      textColor: Color(0xFF959595),
                                    ),
                                  ),
                                  controller: roomHeightController,
                                ),
                              ),
                              SizedBox(width: 17),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 212),
                                child: NarSelectBoxWidget(
                                  label: "Ascensore",
                                  options: ["No","Sì"],
                                  controller: elevatorController,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 27,),
                    //------ Specifiche sui materiali da applicare
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 26,vertical: 18),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Specifiche sui materiali da applicare',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          NarFormLabelWidget(
                            label: 'Inserisci descrizione',
                            fontSize: 13,
                            fontWeight: '600',
                          ),
                          SizedBox(height: 5),
                          NarTextareaWidget(
                            hintText: "",
                            maxLines: 8,
                            minLines: 8,
                            actionKeyboard: TextInputAction.done,
                            controller: specificationDescriptionController,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 27,),
                    //------ Elaborati grafici esecutivi
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 26,vertical: 18),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Elaborati grafici esecutivi',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          ...?widget.renovationQuotation!.cmeInformation?.executiveGraphicDrawingList!.map((element){
                            return Container(
                              padding: EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: Color(0xFFE1E1E1),width: 1)
                              ),
                              margin: EdgeInsets.only(bottom: 10),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        NarFormLabelWidget(
                                          label: element.type ?? "",
                                          fontSize: 15,
                                          fontWeight: '700',
                                          textColor: AppColor.black,
                                        ),
                                        NarFilePickerWidget(
                                          allowMultiple: true,
                                          allowedExtensions: ['pdf',"jpeg",'jpg','png'],
                                          filesToDisplayInList: 0,
                                          removeButton: false,
                                          isDownloadable: false,
                                          removeButtonText: 'Elimina',
                                          removeButtonTextColor: Color(0xff797979),
                                          uploadButtonPosition: 'back',
                                          showMoreButtonText: '+ espandi',
                                          actionButtonPosition: 'bottom',
                                          displayFormat: 'inline-button',
                                          containerWidth: 65,
                                          containerHeight: 65,
                                          containerBorderRadius: 8,
                                          borderRadius: 7,
                                          fontSize: 11,
                                          fontWeight: '600',
                                          text: 'Carica',
                                          borderSideColor: Theme.of(context).primaryColor,
                                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                          allFiles: element.imageList,
                                          pageContext: context,
                                          storageDirectory: "renovationQuotation/${element.type}/${widget.renovationQuotation?.id}",
                                          removeExistingOnChange: false,
                                          progressMessage: [''],
                                          notAccent: true,
                                          splashColor: Color(0xffE5E5E5),
                                          height: 35,
                                          buttonWidth: 125,
                                          buttonTextColor: Colors.black,
                                          onUploadCompleted: () {
                                            if (mounted) {
                                              setState(() {});
                                            }
                                          },
                                        )
                                      ],
                                    ),
                                    element.imageList?.isNotEmpty ?? false ?
                                    Padding(
                                      padding: const EdgeInsets.only(top:10 ),
                                      child: NarFilePickerWidget(
                                        allowMultiple: true,
                                        filesToDisplayInList: 0,
                                        removeButton: true,
                                        isDownloadable: false,
                                        removeButtonText: 'Elimina',
                                        removeButtonTextColor: Color(0xff797979),
                                        uploadButtonPosition: 'back',
                                        showMoreButtonText: '+ espandi',
                                        actionButtonPosition: 'bottom',
                                        displayFormat: 'inline-widget',
                                        containerWidth: 86,
                                        containerHeight: 86,
                                        containerBorderRadius: 8,
                                        borderRadius: 7,
                                        fontSize: 11,
                                        fontWeight: '600',
                                        text: 'Carica Element',
                                        borderSideColor: Theme.of(context).primaryColor,
                                        hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                        allFiles: element.imageList,
                                        pageContext: context,
                                        storageDirectory: "renovationQuotation/${element.type}/${widget.renovationQuotation?.id}",
                                        removeExistingOnChange: true,
                                        progressMessage: [''],
                                        notAccent: true,
                                        showTitle: false,
                                        onUploadCompleted: () {
                                          if (mounted) {
                                            setState(() {});
                                          }
                                        },
                                      ),
                                    ) : SizedBox(),
                                  ],
                              ),
                            );
                          })
                        ],
                      ),
                    ),
                    SizedBox(height: 27,),
                    //------ Virtual Tour Stato di Fatto
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 26,vertical: 18),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Virtual Tour Stato di Fatto',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          Column(
                            children: [
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: 'Link del virtual tour',
                                    hintText: "",
                                    controller: virtualTourController,
                                    minLines: 1,
                                    onTap: () async {},
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'QR code',
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textColor: Color.fromRGBO(0, 0, 0, 1),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    virtualTourController.text.isNotEmpty ?
                                    Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          QrImageView(
                                            data: virtualTourController.text,
                                            version: QrVersions.auto,
                                            size: 160.0,
                                          ),
                                        ])
                                        :
                                    NarFormLabelWidget(
                                      label: 'QR code Virtual Tour stato di fatto',
                                      fontSize: 13,
                                      fontWeight: '600',
                                      textColor: Colors.black,
                                    ),
                                  ])
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          //------Salva Button

          formMessages.isNotEmpty ?
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                NarFormLabelWidget(label: formMessages[0]),
              ],
            ),
          ) : SizedBox.shrink(),
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                BaseNewarcButton(
                    buttonText: 'Scarica PDF',
                    textColor: Color(0xFF555555),
                    color: Color(0xFFD9D9D9),
                    fontWeight: "600",
                    onPressed: () async {
                      await saveCMEInformation();
                      Map<String, bool> lavoriCategoryMap = {};
                      Map<String, bool> fornitureCategoryMap = {};
                      widget.renovationQuotation?.renovationActivity?.forEach((cat){
                        if(cat.category == "C - Lavori interni"){
                          cat.activity?.forEach((act){
                            lavoriCategoryMap[act.subCategory!] = false;
                          });
                        }
                        if(cat.category == "M - Forniture"){
                          cat.activity?.forEach((act){
                            fornitureCategoryMap[act.subCategory!] = false;
                          });
                        }
                      });
                      showSelectCategoryForCMEDialog(context: context,lavoriSubCategoryMap: lavoriCategoryMap,fornitureSubCategoryMap: fornitureCategoryMap,selectedRenovationQuotation: widget.renovationQuotation!);
                    }
                ),
                SizedBox(width: 16,),
                BaseNewarcButton(
                    buttonText: 'Salva',
                    fontWeight: "600",
                    onPressed: () async {
                      await saveCMEInformation();
                    }
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Future<void> saveCMEInformation() async {
    setState(() {
      formMessages.clear();
      formMessages.add('Saving');
    });

    try {
      widget.renovationQuotation!.cmeInformation?.specificationDescription = specificationDescriptionController.text.trim();
      widget.renovationQuotation!.cmeInformation?.blueStripes = blueStripesController.text.trim();
      widget.renovationQuotation!.cmeInformation?.drivewayAccess = drivewayAccessController.text.trim();
      widget.renovationQuotation!.cmeInformation?.elevator = elevatorController.text.trim();
      widget.renovationQuotation!.cmeInformation?.limitedTrafficZone = limitedTrafficZoneController.text.trim();
      widget.renovationQuotation!.cmeInformation?.possibilityOfFreightElevator = possibilityOfFreightElevatorController.text.trim();
      widget.renovationQuotation!.cmeInformation?.possibilityOfRubbleContainer = possibilityOfRubbleContainerController.text.trim();
      widget.renovationQuotation!.cmeInformation?.projectDescription = projectDescriptionController.text.trim();
      widget.renovationQuotation!.cmeInformation?.roomHeight = double.tryParse(roomHeightController.text.trim().replaceAll(".", "").replaceAll(",", "."));
      widget.renovationQuotation!.cmeInformation?.walkableSquareMeters = double.tryParse(walkableSquareMetersController.text.trim().replaceAll(".", "").replaceAll(",", "."));
      widget.renovationQuotation!.cmeInformation?.virtualTourURL = virtualTourController.text.trim();

      widget.renovationQuotation!.cmeInformation?.executiveGraphicDrawingList?.forEach((val){
        val.filePath = [];
        if(val.imageList?.isNotEmpty ?? false){
          for(var img in val.imageList!){
           Map<String,dynamic> path = {
              "location":"renovationQuotation/${val.type}/${widget.renovationQuotation?.id}",
              "fileName": img,
            };
            val.filePath!.add(path);
          }

        }
      });

      await FirebaseFirestore.instance
          .collection(appConfig
          .COLLECT_RENOVATION_QUOTATION)
          .doc(widget.renovationQuotation!.id)
          .update({"cmeInformation":widget.renovationQuotation!.cmeInformation!.toMap()});

      setState(() {
        formMessages.clear();
        formMessages.add('Saved!');
      });

    } catch (e, s) {
      log("saveQuotation ERROR --------> ${e.toString()}");
      log("saveQuotation STACKTRACE --------> ${s.toString()}");
      print({e, s});
      setState(() {
        formMessages.clear();
        formMessages.add('Error occured');
      });
    }
  }

  void showSelectCategoryForCMEDialog({required BuildContext context,required Map<String, bool> lavoriSubCategoryMap,required Map<String, bool> fornitureSubCategoryMap,required RenovationQuotation selectedRenovationQuotation}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext _context) {
          BuildContext parentContext = context;
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Scarica CME"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Scarica CME",
                  buttonText: "Scarica",
                  onPressed: () async {
                    Future.delayed(Duration(milliseconds: 10),(){
                      _showDownloadingDialog(parentContext);
                    });
                    try{
                      List<String> selectedLavoriSubCategory = [
                        ...lavoriSubCategoryMap.entries.where((e) => e.value).map((e) => e.key),
                      ];

                      List<String> selectedFornitureSubCategory = [
                        ...fornitureSubCategoryMap.entries.where((e) => e.value).map((e) => e.key),
                      ];

                     await downloadComputoMatricoQuotationPDF(row: selectedRenovationQuotation,selectedLavoriSubCategory: selectedLavoriSubCategory,selectedFornitureSubCategory: selectedFornitureSubCategory);
                    }catch(e){
                      log("Error while downloading CME PDF ----> ${e.toString()}");
                    }finally{
                      Future.delayed(Duration(milliseconds: 200), () {
                        Navigator.of(parentContext, rootNavigator: true).pop();
                      });
                    }
                  },
                  column: SizedBox(
                    width: 800,
                    height: MediaQuery.of(context).size.height * .50,
                    child:  Container(
                      width: 600,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7),
                          border: Border.all(color: Color(0xFFA4A4A4),width: 1)
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Container(
                              // width: 300,
                              height: MediaQuery.of(context).size.height * .45,
                              padding: EdgeInsets.symmetric(vertical: 25),
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 10),
                                    child: NarFormLabelWidget(
                                      label: "Lavori",
                                      fontSize: 14,
                                      fontWeight: "700",
                                    ),
                                  ),
                                  SizedBox(height: 10,),
                                  NarCheckboxWidget(
                                    label: "",
                                    values: lavoriSubCategoryMap,
                                    columns: 1,
                                    fontSize: 13,
                                    listTileBoxWidthHeight: 20,
                                    childAspectRatio: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(width: 1,color: Color(0xFFA4A4A4),height: MediaQuery.of(context).size.height * .50,),
                          Expanded(
                            child: Container(
                              // width: 300,
                              height: MediaQuery.of(context).size.height * .45,
                              padding: EdgeInsets.symmetric(vertical: 25),
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 10),
                                    child: NarFormLabelWidget(
                                      label: "Forniture",
                                      fontSize: 14,
                                      fontWeight: "700",
                                    ),
                                  ),
                                  SizedBox(height: 10,),
                                  NarCheckboxWidget(
                                    label: "",
                                    values: fornitureSubCategoryMap,
                                    columns: 1,
                                    fontSize: 13,
                                    listTileBoxWidthHeight: 20,
                                    childAspectRatio: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )),
            );
          });
        });
  }

  Future<void> downloadComputoMatricoQuotationPDF({required RenovationQuotation row,required List<String> selectedLavoriSubCategory,required List<String> selectedFornitureSubCategory})async{
    try{
      if(selectedLavoriSubCategory.isEmpty && selectedFornitureSubCategory.isEmpty) return;

      Map<String, Map<String, List<Map>>> renovationData = {};
      Map<String, Map> subCategoryTotal = {};
      if (row.renovationActivity != null) {
        for (var i = 0; i < row.renovationActivity!.length; i++) {
          RenovationActivityCategory tmpRenovationActivityCategory = row.renovationActivity![i];
          String category = tmpRenovationActivityCategory.category!;

          if (!renovationData.containsKey(category)) {
            renovationData[category] = {};
          }

          for (var j = 0; j < tmpRenovationActivityCategory.activity!.length; j++) {
            String subCategory = tmpRenovationActivityCategory.activity![j].subCategory!;

            Map rowData = {
              'index': tmpRenovationActivityCategory.activity![j].index,
              'title': tmpRenovationActivityCategory.activity![j].title,
              'measurementUnit': tmpRenovationActivityCategory.activity![j].measurementUnit,
              'quantity': tmpRenovationActivityCategory.activity![j].quantity,
              'unitPrice': tmpRenovationActivityCategory.activity![j].unitPrice,
              'description': tmpRenovationActivityCategory.activity![j].description,
              'priceLevel': tmpRenovationActivityCategory.activity![j].priceLevel,
              'subCategory': tmpRenovationActivityCategory.activity![j].subCategory,
              'code': tmpRenovationActivityCategory.activity![j].code,
              'comment': tmpRenovationActivityCategory.activity![j].comment,
              'isDiscounted': tmpRenovationActivityCategory.activity![j].isDiscounted,
              'isManualActivity': tmpRenovationActivityCategory.activity![j].isManualActivity,
              'isIncludedInComputoMatric': tmpRenovationActivityCategory.activity![j].isIncludedInComputoMatric,
              'total': 0,
            };

            if (!renovationData[category]!.containsKey(subCategory)) {
              renovationData[category]![subCategory] = [];
            }

            if( !subCategoryTotal.containsKey(category) ){
              subCategoryTotal[category] = {};
            }
            if (!subCategoryTotal[category]!.containsKey(subCategory)) {
              subCategoryTotal[category]![subCategory] = 0;
            }

            if(tmpRenovationActivityCategory.activity![j].isIncludedInComputoMatric ?? true){
              renovationData[category]![subCategory]!.add(rowData);
            }
          }
        }
      }
      await computoMatrixPdfDesign(quotation: row,renovationData: renovationData,subCategoryTotal: subCategoryTotal,selectedLavoriSubCategory: selectedLavoriSubCategory,selectedFornitureSubCategory: selectedFornitureSubCategory);
    }finally{

    }
  }

  void _showDownloadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      useRootNavigator: true,
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                //color: Colors.black87,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColor.white,),
                  SizedBox(height: 10,),
                  NarFormLabelWidget(
                    textAlign: TextAlign.center,
                    label: "Creando PDF...",
                    fontSize: 18,
                    fontWeight: '700',
                    textColor: AppColor.white,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}


