import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class RenovationContact {
  String? id;
  @Deprecated('Use RenovationContact.personInfo.name instead, keep in mind retrocompatibility')
  String? name;
  @Deprecated('Use RenovationContact.personInfo.surname instead, keep in mind retrocompatibility')
  String? surname;
  @Deprecated('Use RenovationContact.personInfo.email instead, keep in mind retrocompatibility')
  String? email;
  @Deprecated('Use RenovationContact.personInfo.phone instead, keep in mind retrocompatibility')
  String? phone;
  BasePersonInfo? personInfo;
  int? created;
  String? contactStatus;
  String? renovationStatus;
  String? assignedRenovatorId;
  String? assignedRenovationProjectId;
  @Deprecated('Use RenovationContact.addressInfo.fullAddress instead, keep in mind retrocompatibility')
  String? streetAddress;
  @Deprecated('Use RenovationContact.addressInfo.city instead, keep in mind retrocompatibility')
  String? city;
  List<String>? addressInfo = [];
  List? files;
  String? assignedQuotation;
  late bool isFirebaseUser;
  int? firebaseUserCreated;
  // attributes of agency suggested renovation contacts
  late bool isSuggestedContact;
  String? agencyId;
  num? renovationPrice;
  num? agencyCommission;
  String? suggestionStatus; // "segnalato", "acquisito", "non-acquisito"
  late bool isArchived;
  late bool isRequestingQuotation; // signals whether to show suggested contact in "clienti ristrutturazione" as well as "ristrutturazioni segnalate"


  RenovationContact(Map<String, dynamic> newarcProjectMap) {
    this.name = newarcProjectMap['name'];
    this.surname = newarcProjectMap['surname'];
    this.email = newarcProjectMap['email'];
    this.phone = newarcProjectMap['phone'];
    this.personInfo = (newarcProjectMap.containsKey('personInfo') && newarcProjectMap['personInfo'] != null) ? BasePersonInfo.fromMap(newarcProjectMap['personInfo']) : null;
    this.created = newarcProjectMap['created'];
    this.contactStatus = newarcProjectMap['contactStatus'];
    this.renovationStatus = newarcProjectMap['renovationStatus'];
    this.streetAddress = newarcProjectMap['streetAddress'];
    this.city = newarcProjectMap['city'];
    
    this.addressInfo = newarcProjectMap.containsKey('addressInfo') && newarcProjectMap['addressInfo'] != null ? newarcProjectMap['addressInfo'] : [];

    this.assignedRenovatorId = newarcProjectMap['assignedRenovatorId'];
    this.assignedRenovationProjectId = newarcProjectMap['assignedRenovationProjectId'];
    this.files = newarcProjectMap['files'].length != null ? newarcProjectMap['files'] : [];
    this.assignedQuotation = newarcProjectMap['assignedQuotation'] != null ? newarcProjectMap['assignedQuotation'] : null;
    this.isFirebaseUser = newarcProjectMap['isFirebaseUser'] ?? false;
    this.firebaseUserCreated = newarcProjectMap['firebaseUserCreated'];
    this.isSuggestedContact = newarcProjectMap['isSuggestedContact'] ?? false;
    this.agencyId = newarcProjectMap['agencyId'];
    this.renovationPrice = newarcProjectMap['renovationPrice'];
    this.agencyCommission = newarcProjectMap['agencyCommission'];
    this.suggestionStatus = newarcProjectMap['suggestionStatus'];
    this.isArchived = newarcProjectMap['isArchived'] ?? false;
    this.isRequestingQuotation = newarcProjectMap['isRequestingQuotation'] ?? true;

  }

  RenovationContact.empty() {
    this.id = '';
    this.name = '';
    this.surname = '';
    this.email = '';
    this.phone = '';
    this.personInfo = BasePersonInfo.empty();
    this.created = Timestamp.now().millisecondsSinceEpoch;
    this.contactStatus = 'primo-incontro';
    this.renovationStatus = '';
    this.assignedRenovatorId = '';
    this.assignedRenovationProjectId = '';
    this.streetAddress = '';
    this.city = '';
    this.addressInfo = [];
    this.assignedRenovatorId = '';
    this.files = [];
    this.assignedQuotation = null;
    this.isFirebaseUser = false;
    this.firebaseUserCreated = null;
    this.isSuggestedContact = false;
    this.agencyId = null;
    this.renovationPrice = null;
    this.agencyCommission = null;
    this.suggestionStatus = null;
    this.isArchived = false;
    this.isRequestingQuotation = true;

  }

  RenovationContact.fromDocument(Map<String, dynamic> data, String id) {
    
    
      try {
        
      
    

      this.id = id;
      this.name = data['name'];
      this.surname = data['surname'];
      this.email = data['email'];
      this.phone = data['phone'];
      this.personInfo = (data.containsKey('personInfo') && data['personInfo'] != null) ? BasePersonInfo.fromMap(data['personInfo']) : null;
      this.created = data['created'];
      this.contactStatus = data['contactStatus']??'';
      this.renovationStatus = data['renovationStatus']??'';
      this.assignedRenovatorId = data['assignedRenovatorId']??'';
      this.streetAddress = data['streetAddress']??'';
      this.city = data['city'] == null ? '' : data['city'];
      this.files = data['files'] == null ? [] : data['files'];
      this.assignedQuotation = data['assignedQuotation'] == null ? null : data['assignedQuotation'];
      this.isFirebaseUser = data['isFirebaseUser'] ?? false;
      this.firebaseUserCreated = data['firebaseUserCreated'];
      this.isSuggestedContact = data['isSuggestedContact'] ?? false;
      this.agencyId = data['agencyId'];
      this.renovationPrice = data['renovationPrice'];
      this.agencyCommission = data['agencyCommission'];
      this.suggestionStatus = data['suggestionStatus'];
      this.isArchived = data['isArchived'] ?? false;
      this.isRequestingQuotation = data['isRequestingQuotation'] ?? true;

      try {
        this.addressInfo = data.containsKey('addressInfo') && data['addressInfo'] != null is Map<String, dynamic>
        ? (data['addressInfo'] as List).map((item) => item.toString()).toList()
        : [];
      } catch (e){
        this.addressInfo = [];

        if( data['addressInfo'] is Map ) {
          
          BaseAddressInfo address = BaseAddressInfo.fromMap(data['addressInfo']);
          RenovationContactAddress renovationContactAddress = RenovationContactAddress({
            'renovationContactid': id,
            'created': DateTime.now().millisecondsSinceEpoch,
            'addressInfo': address,
            'isArchived': false
          });

          fallbackAddAddress(id, renovationContactAddress);

          

        }
        
      }

      } catch (e) {
        debugPrint(e.toString());
        debugPrintStack();
      }
    
  }

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'surname': this.surname,
      'email': this.email,
      'phone': this.phone,
      'personInfo': this.personInfo?.toMap(),
      'created': this.created,
      'contactStatus': this.contactStatus,
      'renovationStatus': this.renovationStatus,
      'assignedRenovatorId': this.assignedRenovatorId,
      'assignedRenovationProjectId': this.assignedRenovationProjectId,
      'streetAddress': this.streetAddress,
      'city': this.city,
      'addressInfo': this.addressInfo,
      'files': this.files,
      'assignedQuotation': this.assignedQuotation,
      'isFirebaseUser': this.isFirebaseUser,
      'firebaseUserCreated': this.firebaseUserCreated,
      'isSuggestedContact': this.isSuggestedContact,
      'agencyId': this.agencyId,
      'renovationPrice': this.renovationPrice,
      'agencyCommission': this.agencyCommission,
      'suggestionStatus': this.suggestionStatus,
      'isArchived': this.isArchived,
      'isRequestingQuotation': this.isRequestingQuotation,
    };
  }

  fallbackAddAddress( String renovationContactId, RenovationContactAddress address ) async {

    DocumentReference<Map<String, dynamic>> addedAddress = await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
    .add(address.toMap());


    await FirebaseFirestore.instance
    .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
    .doc(renovationContactId)
    .update({ 'addressInfo': [ addedAddress.id ] });
  }
}

Map<String, String> stringMap = {'primo-incontro': 'Primo incontro'};
